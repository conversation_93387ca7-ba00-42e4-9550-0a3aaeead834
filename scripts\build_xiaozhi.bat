@echo off
chcp 65001 > nul
title <PERSON><PERSON><PERSON> ESP32 Command Manager - Build Tool
echo ========================================
echo XiaoZhi ESP32 Command Manager - Build Tool v1.0
echo ========================================
echo.
echo Starting build process...
echo Current directory: %CD%
echo.

:: 设置Python路径 - 自动检测多个可能的Python路径
set "PYTHON_PATH="
for %%p in (
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"
    "C:\Python310\python.exe"
    "C:\Python311\python.exe"
    "C:\Python312\python.exe"
    "python.exe"
) do (
    if exist %%p (
        set "PYTHON_PATH=%%~p"
        goto :found_python
    )
)

:found_python
if "%PYTHON_PATH%"=="" (
    echo Error: Python installation not found
    echo Please ensure Python is correctly installed and in PATH
    echo Supported Python versions: 3.9, 3.10, 3.11, 3.12
    pause
    exit /b 1
)

echo Found Python: %PYTHON_PATH%
echo.

:: Check if main program file exists
if not exist "main.py" (
    echo Error: main.py file not found
    echo Please ensure you are running this script in the correct directory
    pause
    exit /b 1
)

:: Check necessary directory structure
if not exist "gui" (
    echo Error: gui directory not found
    echo Please ensure project structure is complete
    pause
    exit /b 1
)

if not exist "core" (
    echo Error: core directory not found
    echo Please ensure project structure is complete
    pause
    exit /b 1
)

echo Installing/updating required packages...
"%PYTHON_PATH%" -m pip install --upgrade pip
"%PYTHON_PATH%" -m pip install pyinstaller
"%PYTHON_PATH%" -m pip install pyserial
"%PYTHON_PATH%" -m pip install -r requirements.txt

echo.
echo Building executable file...
echo This may take a few minutes, please wait...
echo.

:: Clean previous build files
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"

:: 使用PyInstaller打包
"%PYTHON_PATH%" -m PyInstaller ^
    --clean ^
    --noconfirm ^
    --onefile ^
    --windowed ^
    --name "小智ESP32指令管理器" ^

    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.filedialog ^
    --hidden-import tkinter.simpledialog ^
    --hidden-import serial ^
    --hidden-import serial.tools.list_ports ^
    --hidden-import logging ^
    --hidden-import threading ^
    --hidden-import json ^
    --hidden-import configparser ^
    --hidden-import datetime ^
    --hidden-import time ^
    --hidden-import re ^
    --hidden-import os ^
    --hidden-import sys ^
    --collect-all serial ^
    --collect-all serial.tools ^
    --add-data "config;config" ^
    --add-data "logs;logs" ^
    main.py

echo.
if exist "dist\小智ESP32指令管理器.exe" (
    echo ========================================
    echo 🎉 打包成功！
    echo ========================================
    echo.
    echo 可执行文件位置: dist\小智ESP32指令管理器.exe
    echo 文件大小: 
    for %%f in ("dist\小智ESP32指令管理器.exe") do echo   %%~zf 字节
    echo.
    echo 📋 使用说明:
    echo 1. 可执行文件是独立的，无需安装Python即可运行
    echo 2. 可以将exe文件复制到任何Windows电脑上使用
    echo 3. 首次运行时可能被杀毒软件拦截，请添加信任
    echo 4. 确保目标电脑有USB转串口驱动程序
    echo.
    echo 📁 打包内容:
    echo   - 主程序 (main.py)
    echo   - GUI界面模块 (gui/*)
    echo   - 核心功能模块 (core/*)
    echo   - 工具模块 (utils/*)
    echo   - 配置文件模板
    echo   - 日志目录
    echo.
    echo ✅ 您现在可以分发这个exe文件了！
) else (
    echo ========================================
    echo ❌ 打包失败
    echo ========================================
    echo.
    echo 请检查上面的错误信息。
    echo 常见问题:
    echo 1. Python版本不兼容 (需要3.9+)
    echo 2. 缺少必要的包
    echo 3. 磁盘空间不足
    echo 4. 权限不足 (尝试以管理员身份运行)
    echo.
)

echo.
echo 清理临时文件...
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo.
echo 按任意键退出...
pause > nul
