#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口通信模块
实现与ESP32设备的UART2通信
使用简化文本协议：CMD:参数1:参数2\n
"""

import serial
import serial.tools.list_ports
import time
import threading
import logging
import json
import re
from typing import Optional, Dict, Any, List, List
from queue import Queue, Empty

class ProtocolHandler:
    """协议处理类"""

    @staticmethod
    def escape_string(text: str) -> str:
        """字符串转义"""
        if not text:
            return ""
        return text.replace('\\', '\\\\').replace('|', '\\p').replace('\n', '\\n').replace('\r', '\\r').replace(':', '\\c')

    @staticmethod
    def unescape_string(text: str) -> str:
        """字符串反转义"""
        if not text:
            return ""
        return text.replace('\\c', ':').replace('\\p', '|').replace('\\\\', '\\').replace('\\n', '\n').replace('\\r', '\r')

    @staticmethod
    def build_command(cmd: str, *params) -> str:
        """构建命令"""
        if params:
            escaped_params = [ProtocolHandler.escape_string(str(p)) for p in params]
            return f"{cmd}:{':'.join(escaped_params)}\n"
        return f"{cmd}\n"

    @staticmethod
    def parse_response(response: str) -> Dict[str, Any]:
        """解析响应"""
        response = response.strip()
        if not response:
            return {"status": "error", "error": "empty response"}

        if response == "PONG":
            return {"status": "success", "type": "pong"}
        elif response.startswith("OK:"):
            data = response[3:] if len(response) > 3 else ""
            return {"status": "success", "data": ProtocolHandler.unescape_string(data)}
        elif response.startswith("ERROR:"):
            error = response[6:] if len(response) > 6 else "unknown error"
            return {"status": "error", "error": ProtocolHandler.unescape_string(error)}
        elif response.startswith("OK"):
            return {"status": "success", "data": ""}
        elif response.startswith("K:"):
            # 处理缺少"O"的情况，如"K:help,wifi,4g,reboot,ota,settings"
            data = response[2:] if len(response) > 2 else ""
            return {"status": "success", "data": ProtocolHandler.unescape_string(data)}
        else:
            # 检查是否是新协议的标记格式
            if (response.endswith("_START") or response.endswith("_END") or
                response == "ERROR_START" or response == "ERROR_END"):
                return {"status": "marker", "raw": response}
            else:
                return {"status": "unknown", "raw": response}

class SerialCommunication:
    """串口通信类"""

    def __init__(self):
        self.serial_port: Optional[serial.Serial] = None
        self.is_connected = False
        self.response_queue = Queue()
        self.read_thread: Optional[threading.Thread] = None
        self.stop_reading = False
        self.logger = logging.getLogger(__name__)
        self.protocol = ProtocolHandler()
        
    def get_available_ports(self) -> List[str]:
        """获取可用串口列表"""
        try:
            ports = [port.device for port in serial.tools.list_ports.comports()]
            self.logger.info(f"发现可用串口: {ports}")
            return ports
        except Exception as e:
            self.logger.error(f"获取串口列表失败: {e}")
            return []
    
    def connect(self, port: str, baudrate: int = 19200) -> bool:
        """
        连接到ESP32设备
        
        Args:
            port: 串口名称 (如 COM3, /dev/ttyUSB0)
            baudrate: 波特率，默认19200
            
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.is_connected:
                self.disconnect()
            
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=1,
                write_timeout=1
            )
            
            # 清空缓冲区
            self.serial_port.reset_input_buffer()
            self.serial_port.reset_output_buffer()
            
            self.is_connected = True
            self.stop_reading = False
            
            # 启动读取线程
            self.read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self.read_thread.start()
            
            self.logger.info(f"成功连接到 {port}，波特率: {baudrate}")
            return True
            
        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            self.stop_reading = True
            self.is_connected = False
            
            # 等待读取线程结束
            if self.read_thread and self.read_thread.is_alive():
                self.read_thread.join(timeout=2)
            
            # 关闭串口
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                
            self.logger.info("已断开串口连接")
            
        except Exception as e:
            self.logger.error(f"断开连接时出错: {e}")
    
    def send_raw_data(self, data: str) -> bool:
        """
        发送原始数据（用于测试）

        Args:
            data: 要发送的原始数据

        Returns:
            bool: 是否发送成功
        """
        if not self.is_connected or not self.serial_port:
            self.logger.error("设备未连接")
            return False

        try:
            self.serial_port.write(data.encode('utf-8'))
            self.logger.info(f"发送原始数据: {repr(data)}")
            return True
        except Exception as e:
            self.logger.error(f"发送原始数据失败: {e}")
            return False

    def send_command(self, cmd: str, *params, timeout: int = 5) -> Optional[Dict]:
        """
        发送命令并等待响应

        Args:
            cmd: 命令类型
            *params: 命令参数
            timeout: 超时时间（秒）

        Returns:
            Dict: 响应数据，失败返回None
        """
        if not self.is_connected or not self.serial_port:
            self.logger.error("设备未连接")
            return None

        try:
            # 清空响应队列
            while not self.response_queue.empty():
                try:
                    self.response_queue.get_nowait()
                except Empty:
                    break

            # 构建文本命令
            command_str = self.protocol.build_command(cmd, *params)
            command_bytes = command_str.encode('utf-8')

            self.serial_port.write(command_bytes)
            self.logger.info(f"发送命令: {command_str.strip()}")

            # 根据命令类型选择处理方法
            if cmd in ["GET_SYSTEM_INFO", "GET_NETWORK_INFO", "GET_AUDIO_INFO", "GET_HARDWARE_INFO", "REMINDER_LIST", "REMINDER_GET", "LAMP_STATUS", "GET_CAR_VOICE_CONFIG"]:
                # 支持新标记格式的命令使用标记格式处理方法
                response = self._wait_for_complete_response_with_markers(cmd, timeout)
            else:
                response = self._wait_for_response(timeout)

            if response:
                if response.get("status") == "success":
                    data = response.get('data', '')
                    # 安全地处理不同类型的数据
                    if isinstance(data, str):
                        data_preview = data[:100]
                    elif isinstance(data, dict):
                        data_preview = f"dict with {len(data)} keys"
                    elif isinstance(data, list):
                        data_preview = f"list with {len(data)} items"
                    else:
                        data_preview = str(data)[:100]
                    self.logger.info(f"命令响应: {response.get('status')} - {data_preview}")
                else:
                    self.logger.warning(f"命令失败: {response.get('error', '未知错误')}")
            else:
                self.logger.error(f"命令超时: {cmd}")

            return response

        except Exception as e:
            self.logger.error(f"发送命令失败: {e}")
            return None

    def ping_test(self) -> bool:
        """
        PING连接测试

        Returns:
            bool: 测试是否成功
        """
        try:
            response = self.send_command("PING", timeout=3)
            if response and response.get("status") == "success" and response.get("type") == "pong":
                self.logger.info("PING测试成功")
                return True
            else:
                self.logger.warning("PING测试失败")
                return False
        except Exception as e:
            self.logger.error(f"PING测试异常: {e}")
            return False
    
    def _read_loop(self):
        """读取线程循环"""
        buffer = ""
        byte_buffer = b""  # 字节缓冲区，用于处理不完整的UTF-8字符

        while not self.stop_reading and self.is_connected:
            try:
                if self.serial_port and self.serial_port.in_waiting > 0:
                    # 读取原始字节
                    raw_bytes = self.serial_port.read(self.serial_port.in_waiting)
                    byte_buffer += raw_bytes

                    # 如果数据较大，等待更多数据到达
                    if len(raw_bytes) > 100:
                        time.sleep(0.05)  # 等待50ms让更多数据到达
                        if self.serial_port.in_waiting > 0:
                            additional_bytes = self.serial_port.read(self.serial_port.in_waiting)
                            byte_buffer += additional_bytes

                    # 尝试解码，保留不完整的字节
                    try:
                        data = byte_buffer.decode('utf-8')
                        byte_buffer = b""  # 解码成功，清空字节缓冲区
                    except UnicodeDecodeError as e:
                        # 如果解码失败，保留不完整的字节，等待更多数据
                        if e.start > 0:
                            # 解码部分成功的数据
                            data = byte_buffer[:e.start].decode('utf-8')
                            byte_buffer = byte_buffer[e.start:]  # 保留未解码的字节
                        else:
                            # 完全无法解码，等待更多数据
                            data = ""

                    buffer += data

                    # 处理完整的行
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        if line:
                            self._process_received_line(line)

                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                if self.is_connected:  # 只在连接状态下记录错误
                    self.logger.error(f"读取数据时出错: {e}")
                break
    
    def _process_received_line(self, line: str):
        """处理接收到的数据行"""
        try:
            response = self.protocol.parse_response(line)
            self.logger.info(f"接收响应: 长度={len(line)}, 内容={line.strip()}")

            # 如果是提醒列表数据，记录详细信息
            if line.startswith("OK:reminder_"):
                self.logger.info(f"提醒数据详情: {line}")

            self.response_queue.put(response)
        except Exception as e:
            self.logger.warning(f"响应解析错误: {line}, 错误: {e}")

    def _wait_for_response(self, timeout: int) -> Optional[Dict]:
        """等待响应"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = self.response_queue.get(timeout=0.1)
                return response
            except Empty:
                continue

        self.logger.warning(f"等待响应超时")
        return None

    def _wait_for_complete_response_with_markers(self, command: str, timeout: int = 10) -> Optional[Dict]:
        """
        根据新的协议格式等待完整的响应数据（使用开始/结束标记）

        协议格式：
        成功: <COMMAND>_START -> OK:<DATA> -> <COMMAND>_END
        错误: ERROR_START -> ERROR:<MESSAGE> -> ERROR_END

        Args:
            command: 发送的命令
            timeout: 超时时间（秒）

        Returns:
            Dict: 完整的响应数据，失败返回None
        """
        try:
            start_time = time.time()
            expected_start_marker = f"{command}_START"
            expected_end_marker = f"{command}_END"

            self.logger.info(f"等待 {command} 命令的完整响应（使用标记格式）...")
            self.logger.debug(f"期望开始标记: {expected_start_marker}")
            self.logger.debug(f"期望结束标记: {expected_end_marker}")

            # 第一步：等待开始标记
            start_marker_found = False
            while time.time() - start_time < timeout:
                try:
                    response = self.response_queue.get(timeout=0.1)
                    if response:
                        status = response.get("status")
                        raw_data = response.get("raw", "")

                        # 只处理标记类型的响应
                        if status == "marker" and raw_data == expected_start_marker:
                            start_marker_found = True
                            self.logger.info(f"收到开始标记: {expected_start_marker}")
                            break
                        elif status == "marker" and raw_data == "ERROR_START":
                            self.logger.warning("收到错误开始标记")
                            return self._handle_error_response(timeout - (time.time() - start_time))
                        else:
                            self.logger.debug(f"跳过非标记数据: {raw_data[:50]}...")
                except Empty:
                    continue

            if not start_marker_found:
                self.logger.error(f"未收到开始标记 {expected_start_marker}，超时")
                return None

            # 第二步：收集数据直到结束标记
            data_lines = []
            while time.time() - start_time < timeout:
                try:
                    response = self.response_queue.get(timeout=0.1)
                    if response:
                        status = response.get("status")
                        raw_data = response.get("raw", "")

                        if status == "marker" and raw_data == expected_end_marker:
                            self.logger.info(f"收到结束标记: {expected_end_marker}")
                            break
                        elif status == "marker" and raw_data == "ERROR_END":
                            self.logger.error("收到错误结束标记，但期望的是数据结束标记")
                            return None
                        elif status != "marker":
                            # 收集所有非标记的数据
                            if raw_data == "OK:{":
                                # 将OK:{转换为{，作为JSON的开始
                                data_lines.append("{")
                                self.logger.debug(f"转换OK:开头行为JSON开始: {raw_data}")
                            elif status == "success" and response.get("data") == "{":
                                # 处理已经被parse_response解析的OK:{情况
                                data_lines.append("{")
                                self.logger.debug(f"收集已解析的JSON开始符号: {{")
                            else:
                                # 优先使用raw_data，如果为空则使用data
                                data_to_add = raw_data if raw_data else response.get("data", "")
                                if data_to_add:
                                    data_lines.append(data_to_add)
                                    self.logger.debug(f"收集数据行: {data_to_add[:50]}...")
                        else:
                            self.logger.debug(f"跳过标记数据: {raw_data}")
                except Empty:
                    continue

            # 第三步：解析收集到的数据
            if data_lines:
                complete_data = '\n'.join(data_lines)
                self.logger.info(f"数据收集完成，共 {len(data_lines)} 行，总长度: {len(complete_data)}")
                return self._parse_response_data(complete_data, command)
            else:
                self.logger.warning(f"{command} 命令没有收到任何数据")
                return None

        except Exception as e:
            self.logger.error(f"等待 {command} 响应失败: {e}")
            return None

    def _handle_error_response(self, remaining_timeout: float) -> Optional[Dict]:
        """
        处理错误响应

        Args:
            remaining_timeout: 剩余超时时间

        Returns:
            Dict: 错误响应数据
        """
        try:
            start_time = time.time()
            error_message = ""

            # 收集错误信息直到ERROR_END
            while time.time() - start_time < remaining_timeout:
                try:
                    response = self.response_queue.get(timeout=0.1)
                    if response:
                        status = response.get("status")
                        raw_data = response.get("raw", "")

                        if status == "marker" and raw_data == "ERROR_END":
                            self.logger.info("收到错误结束标记")
                            break
                        elif status == "error":
                            error_message = response.get("error", "unknown error")
                            self.logger.error(f"设备返回错误: {error_message}")
                        elif raw_data.startswith("ERROR:"):
                            error_message = raw_data[6:]  # 去掉"ERROR:"前缀
                            self.logger.error(f"设备返回错误: {error_message}")
                        else:
                            self.logger.debug(f"错误响应中的其他数据: {raw_data}")
                except Empty:
                    continue

            return {"status": "error", "error": error_message or "unknown error"}

        except Exception as e:
            self.logger.error(f"处理错误响应失败: {e}")
            return {"status": "error", "error": "response parsing failed"}



    def _clear_response_queue(self):
        """清空响应队列"""
        try:
            while not self.response_queue.empty():
                try:
                    self.response_queue.get_nowait()
                except:
                    break
            self.logger.debug("响应队列已清空")
        except Exception as e:
            self.logger.error(f"清空响应队列失败: {e}")

    def _parse_response_data(self, data: str, command: str) -> Optional[Dict]:
        """
        解析响应数据

        Args:
            data: 完整的响应数据
            command: 原始命令

        Returns:
            Dict: 解析后的数据
        """
        try:
            self.logger.info(f"解析 {command} 的响应数据: {data[:200]}...")

            # 处理标准JSON格式的数据
            if data.startswith("OK:"):
                response_data = data[3:]  # 去掉"OK:"前缀
            else:
                # 数据收集时已经处理了OK:{，现在应该是完整的JSON
                response_data = data.strip()

            # 根据命令类型解析数据
            if command in ["GET_SYSTEM_INFO", "GET_NETWORK_INFO", "GET_AUDIO_INFO", "GET_HARDWARE_INFO"]:
                # 直接从原始数据提取信息，不依赖JSON解析
                self.logger.info("直接从原始数据提取系统信息，跳过JSON解析")
                extracted_data = self._extract_json_data_from_buffer(response_data)
                if extracted_data:
                    self.logger.info("成功从原始数据提取系统信息")
                    return {"status": "success", "data": extracted_data}

                # 如果提取失败，尝试JSON解析作为备用方案
                try:
                    json_data = json.loads(response_data)
                    self.logger.info(f"备用JSON解析成功，包含 {len(json_data)} 个顶级字段")
                    return {"status": "success", "data": json_data}
                except json.JSONDecodeError as e:
                    self.logger.warning(f"JSON解析失败: {e}")

                    # 尝试修复JSON格式
                    fixed_json = self._fix_json_format(response_data)
                    if fixed_json:
                        try:
                            json_data = json.loads(fixed_json)
                            self.logger.info(f"成功修复并解析JSON数据，包含 {len(json_data)} 个顶级字段")
                            return {"status": "success", "data": json_data}
                        except json.JSONDecodeError as e2:
                            self.logger.warning(f"修复后的JSON仍然无法解析: {e2}")

                    self.logger.warning("所有解析方法都失败")
                    return None

            elif command == "REMINDER_LIST":
                # 提醒列表数据
                if data.startswith("OK:"):
                    reminder_data = data[3:]  # 去掉"OK:"前缀
                else:
                    reminder_data = data
                reminders = self._parse_reminder_list(reminder_data)
                self.logger.info(f"成功解析提醒列表，包含 {len(reminders)} 个提醒")
                return {"status": "success", "data": {"reminders": reminders}}

            elif command == "GET_CAR_VOICE_CONFIG":
                # 汽车语音配置数据（十六进制格式）
                if data.startswith("OK:"):
                    hex_data = data[3:]  # 去掉"OK:"前缀
                elif data.startswith("ERROR:"):
                    hex_data = data[6:]  # 去掉"ERROR:"前缀
                else:
                    hex_data = data

                # 构建完整的响应格式，包含标记
                complete_response = f"CAR_VOICE_CONFIG_START\n{data}\nCAR_VOICE_CONFIG_END"
                self.logger.info(f"汽车语音配置数据长度: {len(hex_data)}")
                return {"status": "success", "data": complete_response}

            elif command in ["LAMP_STATUS", "REMINDER_GET"]:
                # 简单文本数据
                if data.startswith("OK:"):
                    text_data = data[3:]  # 去掉"OK:"前缀
                else:
                    text_data = data
                return {"status": "success", "data": text_data.strip()}

            else:
                # 其他格式
                return {"status": "success", "data": response_data}

        except Exception as e:
            self.logger.error(f"解析响应数据失败: {e}")
            return None

    def _fix_json_format(self, json_str: str) -> Optional[str]:
        """
        尝试修复JSON格式问题（简化版，适用于标准JSON格式）

        Args:
            json_str: 有问题的JSON字符串

        Returns:
            修复后的JSON字符串，如果无法修复则返回None
        """
        try:
            # 基本清理：移除多余的空白字符
            cleaned = json_str.strip()

            # 尝试直接解析
            json.loads(cleaned)
            return cleaned

        except json.JSONDecodeError:
            try:
                # 如果直接解析失败，尝试基本的格式修复
                # 移除多余的空白字符并规范化
                lines = cleaned.split('\n')
                cleaned_lines = [line.strip() for line in lines if line.strip()]
                fixed_json = '\n'.join(cleaned_lines)

                # 验证修复后的JSON
                json.loads(fixed_json)
                return fixed_json

            except Exception as e:
                self.logger.debug(f"JSON修复失败: {e}")
                return None







    def _parse_reminder_list(self, data: str) -> List[Dict]:
        """
        解析提醒列表数据

        Args:
            data: 提醒列表原始数据

        Returns:
            List[Dict]: 解析后的提醒列表
        """
        try:
            if data == "no reminders":
                return []

            reminders = []
            for item in data.split(','):
                if '=' in item:
                    reminder_id, details = item.split('=', 1)
                    parts = details.split('|')
                    if len(parts) >= 3:
                        title = parts[0]
                        time = parts[1]
                        type_detail = parts[2]

                        reminders.append({
                            'id': reminder_id.strip(),
                            'title': title.strip(),
                            'time': time.strip(),
                            'type': type_detail.strip()
                        })

            return reminders

        except Exception as e:
            self.logger.error(f"解析提醒列表失败: {e}")
            return []





    def _clean_json_buffer(self, json_str: str) -> str:
        """
        清理JSON缓冲区数据

        Args:
            json_str: 原始JSON字符串

        Returns:
            str: 清理后的JSON字符串
        """
        try:
            # 移除控制字符和不可见字符
            cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_str)

            # 修复常见的JSON格式问题
            cleaned = cleaned.replace('\t', ' ')  # 替换制表符
            cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并多个空格
            cleaned = cleaned.strip()

            return cleaned
        except Exception as e:
            self.logger.warning(f"清理JSON数据失败: {e}")
            return json_str

    def _try_fix_json(self, json_str: str) -> Optional[str]:
        """
        尝试修复不完整的JSON数据

        Args:
            json_str: 不完整的JSON字符串

        Returns:
            str: 修复后的JSON字符串，失败返回None
        """
        try:
            # 清理数据
            cleaned = self._clean_json_buffer(json_str)

            # 计算大括号数量
            open_braces = cleaned.count('{')
            close_braces = cleaned.count('}')

            # 如果缺少结束大括号，尝试添加
            if open_braces > close_braces:
                missing_braces = open_braces - close_braces
                fixed = cleaned + '}' * missing_braces
                self.logger.info(f"尝试添加 {missing_braces} 个结束大括号")
                return fixed

            # 大括号数量匹配，可能是其他格式问题
            return cleaned

        except Exception as e:
            self.logger.error(f"修复JSON数据失败: {e}")
            return None

    def _extract_json_data_from_buffer(self, buffer: str) -> Optional[Dict[str, Any]]:
        """
        从缓冲区中提取JSON数据

        Args:
            buffer: 包含JSON片段的缓冲区

        Returns:
            Dict: 提取的JSON数据，失败返回None
        """
        try:
            import re

            # 初始化数据结构
            extracted_data = {
                "hardware": {},
                "network": {"wifi": {}, "4g": {}},
                "audio": {},
                "system": {}
            }

            self.logger.info(f"开始从缓冲区提取JSON数据，缓冲区大小: {len(buffer)}")
            self.logger.debug(f"缓冲区内容预览: {buffer[:200]}...")

            # 提取硬件信息
            chip_match = re.search(r'"chip":\s*"([^"]*)"', buffer)
            if chip_match:
                extracted_data["hardware"]["chip"] = chip_match.group(1)
                self.logger.info(f"✓ 提取到芯片信息: {chip_match.group(1)}")

            version_match = re.search(r'"version":\s*"([^"]*)"', buffer)
            if version_match:
                extracted_data["hardware"]["version"] = version_match.group(1)
                self.logger.info(f"✓ 提取到版本信息: {version_match.group(1)}")

            mac_match = re.search(r'"mac":\s*"([^"]*)"', buffer)
            if mac_match:
                extracted_data["hardware"]["mac"] = mac_match.group(1)
                self.logger.info(f"✓ 提取到MAC地址: {mac_match.group(1)}")

            flash_match = re.search(r'"flash_size":\s*"([^"]*)"', buffer)
            if flash_match:
                extracted_data["hardware"]["flash_size"] = flash_match.group(1)
                self.logger.info(f"✓ 提取到Flash大小: {flash_match.group(1)}")

            psram_match = re.search(r'"psram_size":\s*"([^"]*)"', buffer)
            if psram_match:
                extracted_data["hardware"]["psram_size"] = psram_match.group(1)
                self.logger.info(f"✓ 提取到PSRAM大小: {psram_match.group(1)}")

            # 添加chip_id提取
            chip_id_match = re.search(r'"chip_id":\s*(\d+)', buffer)
            if chip_id_match:
                extracted_data["hardware"]["chip_id"] = int(chip_id_match.group(1))
                self.logger.info(f"✓ 提取到芯片ID: {chip_id_match.group(1)}")

            # 提取4G信息
            iccid_match = re.search(r'"iccid":\s*"([^"]*)"', buffer)
            if iccid_match:
                extracted_data["network"]["4g"]["iccid"] = iccid_match.group(1)
                extracted_data["network"]["4g"]["status"] = "connected"
                self.logger.info(f"✓ 提取到ICCID: {iccid_match.group(1)}")

            operator_match = re.search(r'"operator":\s*"([^"]*)"', buffer)
            if operator_match:
                extracted_data["network"]["4g"]["operator"] = operator_match.group(1)
                self.logger.info(f"✓ 提取到运营商: {operator_match.group(1)}")

            imei_match = re.search(r'"imei":\s*"([^"]*)"', buffer)
            if imei_match:
                extracted_data["network"]["4g"]["imei"] = imei_match.group(1)
                self.logger.info(f"✓ 提取到IMEI: {imei_match.group(1)}")

            signal_match = re.search(r'"signal_strength":\s*"([^"]*)"', buffer)
            if signal_match:
                extracted_data["network"]["4g"]["signal_strength"] = signal_match.group(1)
                self.logger.info(f"✓ 提取到信号强度: {signal_match.group(1)}")

            network_type_match = re.search(r'"network_type":\s*"([^"]*)"', buffer)
            if network_type_match:
                extracted_data["network"]["4g"]["network_type"] = network_type_match.group(1)
                self.logger.info(f"✓ 提取到网络类型: {network_type_match.group(1)}")

            ip_match = re.search(r'"ip":\s*"([^"]*)"', buffer)
            if ip_match:
                extracted_data["network"]["4g"]["ip"] = ip_match.group(1)
                self.logger.info(f"✓ 提取到IP地址: {ip_match.group(1)}")

            # 添加网络状态提取
            status_match = re.search(r'"status":\s*"([^"]*)"', buffer)
            if status_match:
                extracted_data["network"]["4g"]["status"] = status_match.group(1)
                self.logger.info(f"✓ 提取到网络状态: {status_match.group(1)}")

            # 提取音频信息
            volume_match = re.search(r'"volume":\s*(\d+)', buffer)
            if volume_match:
                extracted_data["audio"]["volume"] = int(volume_match.group(1))
                self.logger.info(f"✓ 提取到音量信息: {volume_match.group(1)}")

            mic_match = re.search(r'"mic_status":\s*"([^"]*)"', buffer)
            if mic_match:
                extracted_data["audio"]["mic_status"] = mic_match.group(1)
                self.logger.info(f"✓ 提取到麦克风状态: {mic_match.group(1)}")

            speaker_match = re.search(r'"speaker_status":\s*"([^"]*)"', buffer)
            if speaker_match:
                extracted_data["audio"]["speaker_status"] = speaker_match.group(1)
                self.logger.info(f"✓ 提取到扬声器状态: {speaker_match.group(1)}")

            codec_match = re.search(r'"codec":\s*"([^"]*)"', buffer)
            if codec_match:
                extracted_data["audio"]["codec"] = codec_match.group(1)
                self.logger.info(f"✓ 提取到音频编解码器: {codec_match.group(1)}")

            # 提取系统信息
            uptime_match = re.search(r'"uptime":\s*(\d+)', buffer)
            if uptime_match:
                extracted_data["system"]["uptime"] = int(uptime_match.group(1))
                self.logger.info(f"✓ 提取到运行时间: {uptime_match.group(1)}秒")

            tasks_match = re.search(r'"tasks":\s*(\d+)', buffer)
            if tasks_match:
                extracted_data["system"]["tasks"] = int(tasks_match.group(1))
                self.logger.info(f"✓ 提取到任务数量: {tasks_match.group(1)}")

            sram_match = re.search(r'"sram":\s*"([^"]*)"', buffer)
            if sram_match:
                extracted_data["system"]["sram"] = sram_match.group(1)
                self.logger.info(f"✓ 提取到SRAM使用情况: {sram_match.group(1)}")

            psram_sys_match = re.search(r'"psram":\s*"([^"]*)"', buffer)
            if psram_sys_match:
                extracted_data["system"]["psram"] = psram_sys_match.group(1)
                self.logger.info(f"✓ 提取到PSRAM使用情况: {psram_sys_match.group(1)}")

            flash_sys_match = re.search(r'"flash":\s*"([^"]*)"', buffer)
            if flash_sys_match:
                extracted_data["system"]["flash"] = flash_sys_match.group(1)
                self.logger.info(f"✓ 提取到Flash使用情况: {flash_sys_match.group(1)}")

            # 检查是否提取到了有用的数据
            has_data = any([
                bool(extracted_data["hardware"]),
                bool(extracted_data["network"]["4g"]),
                bool(extracted_data["audio"]),
                bool(extracted_data["system"])
            ])

            if has_data:
                self.logger.info(f"成功从缓冲区提取到数据: 硬件={len(extracted_data['hardware'])}项, 4G={len(extracted_data['network']['4g'])}项, 音频={len(extracted_data['audio'])}项, 系统={len(extracted_data['system'])}项")
                return extracted_data
            else:
                self.logger.warning("未能从缓冲区提取到有效数据")
                return None

        except Exception as e:
            self.logger.error(f"从缓冲区提取JSON数据失败: {e}")
            return None

    def is_port_connected(self) -> bool:
        """检查串口是否已连接"""
        return self.is_connected and self.serial_port and self.serial_port.is_open

    def send_raw_data(self, data: str) -> bool:
        """
        发送原始数据（不等待响应）

        Args:
            data: 要发送的原始数据

        Returns:
            bool: 发送是否成功
        """
        if not self.is_connected or not self.serial_port:
            self.logger.error("设备未连接")
            return False

        try:
            # 发送原始数据（添加换行符确保ESP32能识别）
            raw_data = data + '\n'
            self.serial_port.write(raw_data.encode('utf-8'))
            self.serial_port.flush()
            self.logger.info(f"发送原始数据: '{data}' (实际发送: '{raw_data.strip()}')")
            return True

        except Exception as e:
            self.logger.error(f"发送原始数据失败: {e}")
            return False
