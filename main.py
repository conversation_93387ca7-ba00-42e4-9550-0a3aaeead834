#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智ESP32指令管理系统 - 主程序入口
作者: AI Assistant
版本: 2.0.0
"""

import sys
import os

# 添加src路径到系统路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

def main():
    """主程序入口"""
    try:
        from src.app import <PERSON><PERSON><PERSON>App
        app = XiaoZhiApp()
        app.run()
    except ImportError:
        # 如果新架构还未完成，回退到旧版本
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from gui.main_window import MainWindow
        from core.log_manager import LogManager
        import tkinter as tk
        from tkinter import messagebox
        import logging

        try:
            # 初始化日志管理器
            log_manager = LogManager()
            logger = logging.getLogger(__name__)
            logger.info("小智ESP32指令管理系统启动（兼容模式）")

            # 创建主窗口
            app = MainWindow()

            # 运行应用程序
            app.run()

        except Exception as e:
            error_msg = f"程序启动失败: {str(e)}"
            print(error_msg)

            # 尝试显示错误对话框
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                messagebox.showerror("启动错误", error_msg)
                root.destroy()
            except:
                pass

            sys.exit(1)
    except Exception as e:
        error_msg = f"程序启动失败: {str(e)}"
        print(error_msg)
        sys.exit(1)

if __name__ == "__main__":
    main()