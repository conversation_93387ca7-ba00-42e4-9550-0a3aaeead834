# UART信息查询指南

## 概述

本文档详细说明了ESP32设备通过UART协议提供的系统信息查询功能，包括硬件信息、网络状态、音频设置等。电脑端可以通过发送标准化命令获取设备的实时状态信息。

## 通信协议

### 基本格式
- **波特率**: 19200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

### 命令格式
```
<COMMAND>\n
```

### 响应格式
```
<COMMAND>_START\n
OK:<JSON_DATA>\n
<COMMAND>_END\n

ERROR_START\n
ERROR:<ERROR_MESSAGE>\n
ERROR_END\n
```

## 支持的查询命令

### 1. GET_SYSTEM_INFO - 获取完整系统信息

**命令**: `GET_SYSTEM_INFO`

**描述**: 获取设备的完整系统信息，包括硬件、网络、音频、系统状态等所有信息。

**成功响应示例**:
```
GET_SYSTEM_INFO_START
OK:{
  "hardware": {
    "chip": "ESP32-S3",
    "version": "1.7.5",
    "mac": "24:6F:28:XX:XX:XX",
    "flash_size": "8MB",
    "psram_size": "8MB",
    "chip_id": 0
  },
  "network": {
    "wifi": {
      "ssid": "MyWiFi",
      "ip": "*************",
      "mac": "24:6F:28:XX:XX:XX",
      "rssi": -45,
      "status": "connected"
    },
    "4g": {
      "operator": "中国移动",
      "iccid": "898600XXXXXXXXXX",
      "network_type": "4G",
      "ip": "********",
      "signal": -75,
      "status": "connected"
    }
  },
  "audio": {
    "volume": 80,
    "mic_status": "active",
    "speaker_status": "active",
    "codec": "ES8311"
  },
  "system": {
    "uptime": 3600,
    "sram": "350KB/512KB",
    "psram": "600KB/8192KB",
    "flash": "2.5MB/16MB",
    "tasks": 12
  }
}
GET_SYSTEM_INFO_END
```

**错误响应示例**:
```
ERROR_START
ERROR:system not ready
ERROR_END
```

**可能的错误原因**:
- `system not ready`: 系统未完全初始化
- `memory insufficient`: 内存不足，无法生成完整信息
- `network unavailable`: 网络模块不可用

### 2. GET_NETWORK_INFO - 获取网络信息

**命令**: `GET_NETWORK_INFO`

**描述**: 仅获取网络相关信息，包括WiFi和4G连接状态。

**成功响应示例**:
```
GET_NETWORK_INFO_START
OK:{
  "wifi": {
    "ssid": "MyWiFi",
    "ip": "*************",
    "mac": "24:6F:28:XX:XX:XX",
    "rssi": -45,
    "status": "connected"
  },
  "4g": {
    "operator": "中国移动",
    "iccid": "898600XXXXXXXXXX",
    "network_type": "4G",
    "ip": "********",
    "signal": -75,
    "status": "connected"
  }
}
GET_NETWORK_INFO_END
```

**错误响应示例**:
```
ERROR_START
ERROR:network unavailable
ERROR_END
```

**可能的错误原因**:
- `network unavailable`: 网络模块不可用
- `wifi disconnected`: WiFi未连接
- `4g module error`: 4G模块故障

### 3. GET_AUDIO_INFO - 获取音频信息

**命令**: `GET_AUDIO_INFO`

**描述**: 获取音频系统的当前状态和设置。

**成功响应示例**:
```
GET_AUDIO_INFO_START
OK:{
  "volume": 80,
  "mic_status": "active",
  "speaker_status": "active",
  "codec": "ES8311"
}
GET_AUDIO_INFO_END
```

**错误响应示例**:
```
ERROR_START
ERROR:audio unavailable
ERROR_END
```

**可能的错误原因**:
- `audio unavailable`: 音频模块不可用
- `codec error`: 音频编解码器故障
- `volume control error`: 音量控制失败

### 4. GET_HARDWARE_INFO - 获取硬件信息

**命令**: `GET_HARDWARE_INFO`

**描述**: 获取设备硬件相关信息和系统状态。

**成功响应示例**:
```
GET_HARDWARE_INFO_START
OK:{
  "chip": "ESP32-S3",
  "version": "1.7.5",
  "mac": "24:6F:28:XX:XX:XX",
  "flash_size": "8MB",
  "psram_size": "8MB",
  "chip_id": 0,
  "sram": "350KB/512KB",
  "psram": "600KB/8192KB",
  "flash": "2.5MB/16MB",
  "uptime": 3600
}
GET_HARDWARE_INFO_END
```

**错误响应示例**:
```
ERROR_START
ERROR:hardware detection failed
ERROR_END
```

**可能的错误原因**:
- `hardware detection failed`: 硬件检测失败
- `memory read error`: 内存读取错误
- `chip info unavailable`: 芯片信息不可用

### 5. REMINDER_LIST - 获取提醒列表

**命令**: `REMINDER_LIST`

**描述**: 获取当前所有活跃的提醒任务列表，包括详细的重复规律信息。

**成功响应示例**:
```
REMINDER_LIST_START
OK:mcp_1754137767176490_65933=喝水|2025-08-02 12:34:27|单次提醒,mcp_1754137804285322_65933=上班|2025-08-04 10:00:00|每周提醒(周一),mcp_1754137851599965_65933=起床上班|2025-08-03 09:00:00|每日提醒,mcp_1754137879425516_65933=喝水|2025-09-01 12:00:00|每月提醒(1号)
REMINDER_LIST_END
```

**空列表响应**:
```
REMINDER_LIST_START
OK:no reminders
REMINDER_LIST_END
```

**错误响应示例**:
```
ERROR_START
ERROR:reminder system unavailable
ERROR_END
```

**可能的错误原因**:
- `reminder system unavailable`: 提醒系统不可用
- `storage read error`: 存储读取错误
- `memory insufficient`: 内存不足

### 6. REMINDER_GET - 获取单个提醒详情

**命令**: `REMINDER_GET <reminder_id>`

**描述**: 获取指定ID的提醒详细信息。

**参数**:
- `reminder_id`: 提醒的唯一标识符

**成功响应示例**:
```
REMINDER_GET_START
OK:上班|2025-08-04 10:00:00|每周提醒(周一)
REMINDER_GET_END
```

**错误响应示例**:
```
ERROR_START
ERROR:reminder not found
ERROR_END
```

**可能的错误原因**:
- `reminder not found`: 指定ID的提醒不存在
- `invalid reminder id`: 提醒ID格式无效
- `storage read error`: 存储读取错误

### 7. LAMP_STATUS - 获取灯光状态

**命令**: `LAMP_STATUS`

**描述**: 获取当前灯光控制状态。

**成功响应示例**:
```
LAMP_STATUS_START
OK:lamp_on
LAMP_STATUS_END
```
或
```
LAMP_STATUS_START
OK:lamp_off
LAMP_STATUS_END
```

**错误响应示例**:
```
ERROR_START
ERROR:lamp control unavailable
ERROR_END
```

**可能的错误原因**:
- `lamp control unavailable`: 灯光控制不可用
- `gpio error`: GPIO控制错误
- `hardware fault`: 硬件故障

## 数据字段说明

### 硬件信息 (hardware)
| 字段 | 类型 | 说明 |
|------|------|------|
| chip | string | 芯片型号 (ESP32-S3) |
| version | string | 固件版本号 |
| mac | string | 设备MAC地址 |
| flash_size | string | 闪存大小 (如: "8MB") |
| psram_size | string | PSRAM大小 (如: "8MB") |
| chip_id | number | 芯片唯一ID |

### WiFi信息 (network.wifi)
| 字段 | 类型 | 说明 |
|------|------|------|
| ssid | string | 连接的WiFi名称 |
| ip | string | 分配的IP地址 |
| mac | string | WiFi MAC地址 |
| rssi | number | 信号强度 (dBm，负值) |
| status | string | 连接状态: "connected" / "disconnected" |

### 4G信息 (network.4g)
| 字段 | 类型 | 说明 |
|------|------|------|
| operator | string | 运营商名称 (中国移动/中国联通/中国电信) |
| iccid | string | SIM卡ICCID号码 |
| network_type | string | 网络类型 (2G/3G/4G) |
| ip | string | 分配的IP地址 |
| signal | number | 信号强度 (dBm，负值) |
| status | string | 连接状态: "connected" / "disconnected" |

### 音频信息 (audio)
| 字段 | 类型 | 说明 |
|------|------|------|
| volume | number | 当前音量 (0-100) |
| mic_status | string | 麦克风状态: "active" / "inactive" |
| speaker_status | string | 扬声器状态: "active" / "inactive" |
| codec | string | 音频编解码器型号 |

### 系统信息 (system)
| 字段 | 类型 | 说明 |
|------|------|------|
| uptime | number | 系统运行时间 (秒) |
| sram | string | 内部SRAM使用情况 (已用/总量，如: "350KB/512KB") |
| psram | string | 外部PSRAM使用情况 (已用/总量，如: "600KB/8192KB") |
| flash | string | Flash存储使用情况 (已用/总量，如: "2.5MB/16MB") |
| tasks | number | 当前任务数量 |

### 提醒信息 (reminder)
| 字段 | 类型 | 说明 |
|------|------|------|
| reminder_id | string | 提醒唯一标识符 (格式: mcp_timestamp_random) |
| title | string | 提醒标题/内容 |
| time | string | 提醒时间 (格式: YYYY-MM-DD HH:MM:SS) |
| type_detail | string | 提醒类型详情 (见下方类型说明) |

### 提醒类型详情 (type_detail)
| 类型 | 格式 | 说明 | 示例 |
|------|------|------|------|
| 单次提醒 | `单次提醒` | 只执行一次的提醒 | `单次提醒` |
| 每日提醒 | `每日提醒` | 每天重复的提醒 | `每日提醒` |
| 每周提醒 | `每周提醒(周X)` | 每周指定日期重复 | `每周提醒(周一)` |
| 每月提醒 | `每月提醒(X号)` | 每月指定日期重复 | `每月提醒(1号)` |
| 每年提醒 | `每年提醒(X月)` | 每年指定月份重复 | `每年提醒(1月)` |

**周几对照表**:
- 周日 = 0, 周一 = 1, 周二 = 2, 周三 = 3, 周四 = 4, 周五 = 5, 周六 = 6

**新增功能 (2025-08-02)**:
- 每周提醒现在显示具体的周几信息，如 `每周提醒(周一)`
- 每月提醒现在显示具体的日期信息，如 `每月提醒(1号)`
- 每年提醒现在显示具体的月份信息，如 `每年提醒(1月)`

## 内存字段详细说明

### SRAM (内部静态内存)
- **物理特性**: ESP32-S3芯片内部，512KB容量
- **访问速度**: 极快，CPU直接访问，1-2个时钟周期
- **用途**: 系统代码、中断向量、关键变量、任务栈、DMA缓冲区
- **特性**: 相对静态，变化较少
- **格式示例**: `"350KB/512KB"` (已使用350KB，总容量512KB)

### PSRAM (外部伪静态内存)
- **物理特性**: ESP32-S3芯片外部，8MB容量
- **访问速度**: 较慢，通过SPI接口，10-20个时钟周期
- **用途**: 音频缓冲区、图像数据、网络缓冲区、用户数据
- **特性**: 动态变化，频繁分配释放
- **格式示例**: `"600KB/8192KB"` (已使用600KB，总容量8192KB)

### Flash (闪存存储)
- **物理特性**: 非易失性存储，16MB容量
- **访问速度**: 读取较快，写入较慢
- **用途**: 程序代码、固件、配置文件、用户数据存储
- **特性**: 相对静态，主要在程序更新时变化
- **格式示例**: `"2.5MB/16MB"` (已使用2.5MB，总容量16MB)

### 内存使用分析
```
内存类型    容量      用途                    变化频率
SRAM       512KB     系统核心 + 关键数据      低 (相对静态)
PSRAM      8MB       应用缓冲 + 动态数据      高 (频繁变化)
Flash      16MB      程序代码 + 存储数据      极低 (几乎不变)
```

## 信息更新机制

### 缓存策略
- **固定信息**: 启动时获取一次，存储在缓存中
  - 硬件信息 (芯片型号、版本、MAC地址等)
  - WiFi固定信息 (SSID、MAC地址、IP地址)
  - 4G固定信息 (运营商、ICCID、网络类型、IP地址)

- **实时信息**: 每次查询时获取
  - WiFi信号强度 (RSSI)
  - 4G信号强度
  - 系统状态 (SRAM/PSRAM/Flash使用情况、运行时间等)
  - 音频设置 (音量等)

### 性能特点
- **响应速度**: < 100ms
- **内存占用**: < 5KB
- **CPU负载**: 最小化，主要从缓存读取

## 错误处理

### 错误响应格式
```
ERROR_START
ERROR:<error_message>
ERROR_END
```

### 常见错误
| 错误信息 | 说明 |
|----------|------|
| command not found | 命令不存在 |
| system not ready | 系统未初始化完成 |
| network unavailable | 网络模块不可用 |
| audio unavailable | 音频模块不可用 |
| memory insufficient | 内存不足 |
| storage read error | 存储读取错误 |
| hardware detection failed | 硬件检测失败 |
| reminder system unavailable | 提醒系统不可用 |
| lamp control unavailable | 灯光控制不可用 |
| gpio error | GPIO控制错误 |
| hardware fault | 硬件故障 |

## 使用示例

### Python示例
```python
import serial
import json
import time

# 打开串口
ser = serial.Serial('COM3', 19200, timeout=5)

def query_system_info():
    # 发送查询命令
    ser.write(b'GET_SYSTEM_INFO\n')

    # 等待开始标记
    start_marker = ser.readline().decode('utf-8').strip()
    if start_marker != 'GET_SYSTEM_INFO_START':
        print(f"Unexpected start marker: {start_marker}")
        return None

    # 读取响应数据
    response_lines = []
    while True:
        line = ser.readline().decode('utf-8').strip()
        if line == 'GET_SYSTEM_INFO_END':
            break
        elif line.startswith('ERROR_START'):
            # 处理错误响应
            error_line = ser.readline().decode('utf-8').strip()
            end_marker = ser.readline().decode('utf-8').strip()
            print(f"Error: {error_line}")
            return None
        else:
            response_lines.append(line)

    # 解析JSON数据
    json_data = '\n'.join(response_lines)
    if json_data.startswith('OK:'):
        json_str = json_data[3:]  # 去掉 "OK:" 前缀
        return json.loads(json_str)
    else:
        print(f"Unexpected response format: {json_data}")
        return None
# 查询系统信息
info = query_system_info()
if info:
    print(f"设备: {info['hardware']['chip']}")
    print(f"版本: {info['hardware']['version']}")
    if 'wifi' in info['network']:
        print(f"WiFi: {info['network']['wifi']['ssid']}")
        print(f"信号: {info['network']['wifi']['rssi']} dBm")
    print(f"音量: {info['audio']['volume']}%")
    print(f"SRAM: {info['system']['sram']}")
    print(f"PSRAM: {info['system']['psram']}")
    print(f"Flash: {info['system']['flash']}")

ser.close()
```

### 定时查询示例
```python
def monitor_device():
    while True:
        # 每5秒查询一次网络状态
        ser.write(b'GET_NETWORK_INFO\n')

        # 等待开始标记
        start_marker = ser.readline().decode('utf-8').strip()
        if start_marker != 'GET_NETWORK_INFO_START':
            print(f"Unexpected start marker: {start_marker}")
            continue

        # 读取响应数据
        response_lines = []
        while True:
            line = ser.readline().decode('utf-8').strip()
            if line == 'GET_NETWORK_INFO_END':
                break
            elif line.startswith('ERROR_START'):
                # 处理错误响应
                error_line = ser.readline().decode('utf-8').strip()
                end_marker = ser.readline().decode('utf-8').strip()
                print(f"Error: {error_line}")
                break
            else:
                response_lines.append(line)

        # 解析数据
        if response_lines:
            json_data = '\n'.join(response_lines)
            if json_data.startswith('OK:'):
                network_info = json.loads(json_data[3:])
                if 'wifi' in network_info:
                    rssi = network_info['wifi']['rssi']
                    print(f"WiFi信号强度: {rssi} dBm")

        time.sleep(5)
```

### 提醒查询示例
```python
def query_reminders():
    # 查询提醒列表
    ser.write(b'REMINDER_LIST\n')

    # 等待开始标记
    start_marker = ser.readline().decode('utf-8').strip()
    if start_marker != 'REMINDER_LIST_START':
        print(f"Unexpected start marker: {start_marker}")
        return []

    # 读取响应数据
    response_lines = []
    while True:
        line = ser.readline().decode('utf-8').strip()
        if line == 'REMINDER_LIST_END':
            break
        elif line.startswith('ERROR_START'):
            # 处理错误响应
            error_line = ser.readline().decode('utf-8').strip()
            end_marker = ser.readline().decode('utf-8').strip()
            print(f"Error: {error_line}")
            return []
        else:
            response_lines.append(line)

    # 解析数据
    if response_lines:
        json_data = '\n'.join(response_lines)
        if json_data.startswith('OK:'):
            reminder_data = json_data[3:]  # 去掉"OK:"前缀

            if reminder_data == "no reminders":
                print("没有提醒")
                return []

            # 解析提醒列表
            reminders = []
            for item in reminder_data.split(','):
                if '=' in item:
                    reminder_id, details = item.split('=', 1)
                    title, time, type_detail = details.split('|')

                    reminders.append({
                        'id': reminder_id,
                        'title': title,
                        'time': time,
                        'type': type_detail
                    })

            return reminders

    print("查询失败")
    return []

def parse_reminder_type(type_detail):
    """解析提醒类型详情"""
    if type_detail == "单次提醒":
        return {"type": "once"}
    elif type_detail == "每日提醒":
        return {"type": "daily"}
    elif type_detail.startswith("每周提醒(") and type_detail.endswith(")"):
        weekday = type_detail[5:-1]  # 提取括号内的内容
        return {"type": "weekly", "weekday": weekday}
    elif type_detail.startswith("每月提醒(") and type_detail.endswith(")"):
        day = type_detail[5:-1]  # 提取括号内的内容
        return {"type": "monthly", "day": day}
    elif type_detail.startswith("每年提醒(") and type_detail.endswith(")"):
        month = type_detail[5:-1]  # 提取括号内的内容
        return {"type": "yearly", "month": month}
    else:
        return {"type": "unknown", "raw": type_detail}

# 使用示例
reminders = query_reminders()
for reminder in reminders:
    print(f"提醒ID: {reminder['id']}")
    print(f"标题: {reminder['title']}")
    print(f"时间: {reminder['time']}")

    # 解析类型详情
    type_info = parse_reminder_type(reminder['type'])
    if type_info['type'] == 'weekly':
        print(f"类型: 每周提醒，{type_info['weekday']}")
    elif type_info['type'] == 'monthly':
        print(f"类型: 每月提醒，{type_info['day']}")
    elif type_info['type'] == 'yearly':
        print(f"类型: 每年提醒，{type_info['month']}")
    else:
        print(f"类型: {reminder['type']}")
    print("---")
```

## 注意事项

1. **命令大小写敏感**: 必须使用大写命令
2. **换行符**: 命令必须以`\n`结尾
3. **响应解析**: 必须先读取开始标记，再读取数据，最后读取结束标记
4. **JSON格式**: 响应数据为标准JSON格式
5. **字符编码**: 使用UTF-8编码
6. **超时处理**: 建议设置5秒超时时间（数据可能分片传输）
7. **连接稳定性**: 确保串口连接稳定
8. **查询频率**: 建议查询间隔不少于1秒
9. **标记识别**: 必须正确识别开始和结束标记，确保数据完整性
10. **错误处理**: 必须处理ERROR_START/ERROR_END格式的错误响应

## 版本信息

- **文档版本**: 3.0
- **支持固件**: 1.7.5+
- **最后更新**: 2025-08-02
- **更新内容**:
  - **重大协议更新** (2025-08-02):
    - 所有命令响应增加明确的开始/结束标记
    - 成功响应格式：`<COMMAND>_START` + 数据 + `<COMMAND>_END`
    - 错误响应格式：`ERROR_START` + 错误信息 + `ERROR_END`
    - 更新波特率为19200，提高数据传输稳定性
    - 增加完整的错误处理机制和错误原因说明
    - 更新所有示例代码以支持新的标记格式
  - **新增提醒管理功能** (2025-08-02):
    - 添加 `REMINDER_LIST` 命令，获取提醒列表
    - 添加 `REMINDER_GET` 命令，获取单个提醒详情
    - 增强提醒类型显示，显示具体的重复规律
  - 重新设计系统内存字段，使用SRAM/PSRAM/Flash标准格式 (2025-08-01)
