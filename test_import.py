#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入
"""

import sys
import os

# 添加src路径到系统路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

try:
    print("测试导入汽车语音模块...")
    from core.car_voice.car_voice_manager import CarVoiceManager
    print("✓ CarVoiceManager 导入成功")
    
    from core.car_voice.car_voice_protocol import CarVoiceProtocol
    print("✓ CarVoiceProtocol 导入成功")
    
    from core.car_voice.car_voice_config import CarVoiceConfig
    print("✓ CarVoiceConfig 导入成功")
    
    from gui.tabs.car_voice_tab import CarVoiceTab
    print("✓ CarVoiceTab 导入成功")
    
    print("\n所有模块导入成功！")
    
except Exception as e:
    print(f"导入失败: {str(e)}")
    import traceback
    traceback.print_exc()
