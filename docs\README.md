# 小智ESP32指令管理系统

一个用于管理小智AI设备文本指令的GUI应用程序，通过USB TTL串口与ESP32的UART2通信，使用简化文本协议。

## 功能特性

- ✅ 文本指令的增删改查（最多50个）
- ✅ 系统指令查看（只读）
- ✅ 指令执行和测试
- ✅ 配置备份和恢复
- ✅ 全部重置功能
- ✅ PING连接测试
- ✅ 实时系统状态监控（指令数量、内存使用）
- ✅ 日志记录和查看
- ✅ 完整的GUI界面
- ✅ 多线程处理，界面响应流畅
- ✅ 严格的指令名称验证（字母/数字/下划线，不能以数字开头）

## 系统要求

- Python 3.7+
- Windows/Linux/macOS
- USB TTL转串口模块
- ESP32设备（运行小智AI固件）

## 安装依赖

```bash
pip install -r requirements.txt
```

## 硬件连接

```
电脑USB ←→ USB TTL模块 ←→ ESP32
                ├─ VCC → 3.3V (可选，如果ESP32已供电)
                ├─ GND → GND
                ├─ TX  → GPIO9 (ESP32 UART2 RX)
                └─ RX  → GPIO8 (ESP32 UART2 TX)
```

### 串口参数
- 波特率：19200（指令模式，推荐）或115200
- 数据位：8
- 停止位：1
- 奇偶校验：无
- 流控：无

## 使用方法

### 1. 启动应用程序

```bash
python main.py
```

### 2. 连接设备

1. 在串口连接区选择正确的串口
2. 选择合适的波特率（推荐19200用于指令模式）
3. 点击"连接"按钮
4. 确认连接状态显示为"●已连接"
5. 可选：点击"PING"按钮测试连接

### 3. 管理指令

#### 添加指令
1. 在"文本指令"标签页点击"添加指令"按钮
2. 填写指令名称和文本内容
3. 指令名称规则：只能包含字母、数字、下划线，不能以数字开头，最长32字符
4. 指令内容最长500字符
5. 可选择"添加后立即测试"
6. 点击"确定"保存

#### 编辑指令
1. 在指令列表中双击要编辑的指令
2. 或选中指令后点击"编辑指令"按钮
3. 修改文本内容后点击"确定"

#### 删除指令
1. 选中要删除的指令
2. 点击"删除指令"按钮
3. 确认删除操作

#### 执行指令
1. 选中要执行的指令
2. 点击"执行指令"按钮
3. 查看执行结果

#### 全部重置
1. 点击"全部重置"按钮
2. 确认操作（将删除所有文本指令）

#### 查看系统指令
1. 切换到"系统指令"标签页
2. 查看13个只读的系统指令
3. 选中指令后点击"查看详情"获取更多信息

### 4. 备份和恢复

#### 备份指令
1. 点击工具栏的"备份"按钮
2. 选择保存位置和文件名
3. 备份文件为JSON格式

#### 恢复指令
1. 点击工具栏的"恢复"按钮
2. 选择备份文件
3. 确认恢复操作（会覆盖现有指令）

### 5. 设置配置

1. 点击"设置"按钮打开设置对话框
2. 可配置串口参数、界面选项、日志设置
3. 点击"确定"保存设置

## 通信协议

应用程序使用简化文本协议与ESP32通信：

### 命令格式
```
CMD:参数1:参数2\n
```

### 响应格式
```
OK:数据\n          # 成功响应
ERROR:错误信息\n   # 错误响应
PONG\n            # PING响应
```

### 字符转义
- `\\` → `\`（反斜杠）
- `\p` → `|`（管道符）
- `\c` → `:`（冒号）
- `\n` → 换行符
- `\r` → 回车符

### 支持的命令

#### 连接测试
- `PING` → `PONG`

#### 查询命令
- `LIST_SYS` - 获取系统指令列表
- `LIST_TEXT` - 获取文本指令列表
- `GET_SYS:指令名` - 获取系统指令详情
- `GET_TEXT:指令名` - 获取文本指令详情
- `STATUS` - 获取系统状态

#### 管理命令
- `ADD:指令名:指令内容` - 添加文本指令
- `MOD:指令名:新内容` - 修改文本指令
- `DEL:指令名` - 删除文本指令
- `EXEC:指令名` - 执行指令

#### 批量操作
- `BACKUP` - 备份所有指令
- `RESTORE:备份数据` - 恢复指令
- `RESET` - 重置所有指令

## 项目结构

```
xiaozhi_command_manager/
├── main.py                 # 主程序入口
├── gui/
│   ├── main_window.py     # 主窗口
│   ├── command_dialog.py  # 指令编辑对话框
│   └── settings_dialog.py # 设置对话框
├── core/
│   ├── serial_comm.py     # 串口通信
│   ├── command_manager.py # 指令管理
│   ├── config_manager.py  # 配置管理
│   └── log_manager.py     # 日志管理
├── utils/
│   ├── json_utils.py      # JSON工具
│   └── validators.py      # 数据验证
├── resources/
│   └── config.ini         # 默认配置
├── test_communication.py  # 通信测试脚本
├── requirements.txt       # 依赖列表
└── README.md              # 说明文档
```

## 测试

### 通信测试

运行通信测试脚本来验证与ESP32的连接：

```bash
python test_communication.py
```

测试内容包括：
- 串口连接
- 系统状态查询
- 指令列表获取
- 指令增删改查
- 备份恢复功能

## 故障排除

### 常见问题

1. **无法发现串口**
   - 检查USB TTL模块是否正确连接
   - 确认驱动程序已安装
   - 尝试重新插拔USB连接

2. **连接失败**
   - 检查串口是否被其他程序占用
   - 确认波特率设置正确（默认115200）
   - 检查硬件连接线路

3. **通信超时**
   - 确认ESP32设备正常运行
   - 检查UART2引脚连接（GPIO8/GPIO9）
   - 尝试重启ESP32设备

4. **指令操作失败**
   - 检查设备连接状态
   - 查看日志输出区的错误信息
   - 确认指令名称格式正确

### 日志查看

- 应用程序会自动记录详细日志
- 日志文件默认保存在 `logs/app.log`
- 可在设置中调整日志级别和文件位置
- 界面下方的日志输出区显示实时日志

## 开发信息

- **开发语言**: Python 3
- **GUI框架**: tkinter
- **串口通信**: pyserial
- **数据格式**: JSON
- **版本**: 1.0.0

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.0.0 (2025-07-30)
- 初始版本发布
- 实现所有核心功能
- 完整的GUI界面
- 支持所有文档定义的通信协议
