#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汽车语音功能管理器
作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Dict, Any, Optional, Tuple, List
from core.serial_comm import SerialCommunication
from core.car_voice.car_voice_protocol import CarVoiceProtocol
from core.car_voice.car_voice_config import CarVoiceConfig


class CarVoiceManager:
    """汽车语音功能管理器"""
    
    def __init__(self, serial_comm: Optional[SerialCommunication] = None):
        """初始化汽车语音管理器"""
        self.logger = logging.getLogger(__name__)
        self.serial_comm = serial_comm
        self.protocol = CarVoiceProtocol()
        self.config = CarVoiceConfig()
        
    def set_serial_communication(self, serial_comm: SerialCommunication):
        """设置串口通信对象"""
        self.serial_comm = serial_comm
        self.logger.info("已设置串口通信对象")
    
    def get_config_from_device(self) -> <PERSON><PERSON>[bool, Optional[Dict[int, Dict[str, Any]]], str]:
        """从设备获取汽车语音配置"""
        if not self.serial_comm:
            return False, None, "串口通信未初始化"
        
        try:
            self.logger.info("开始获取汽车语音配置...")
            
            # 发送获取配置命令
            success, response = self.serial_comm.send_command("GET_CAR_VOICE_CONFIG")
            
            if not success:
                return False, None, f"获取配置失败: {response}"
            
            # 解析响应数据
            if "CAR_VOICE_CONFIG_START" in response:
                # 提取十六进制数据
                lines = response.split('\n')
                hex_data = ""
                in_data_section = False

                for line in lines:
                    line = line.strip()
                    if line == "CAR_VOICE_CONFIG_START":
                        in_data_section = True
                        continue
                    elif line == "CAR_VOICE_CONFIG_END":
                        break
                    elif in_data_section:
                        # 处理ESP32返回的ERROR:前缀
                        if line.startswith("ERROR:"):
                            hex_data += line[6:]  # 移除"ERROR:"前缀
                        elif line.startswith("OK:"):
                            hex_data += line[3:]  # 移除"OK:"前缀
                        else:
                            hex_data += line

                if not hex_data:
                    return False, None, "未找到配置数据"

                self.logger.info(f"提取到十六进制数据: {hex_data[:100]}...")

                # 解码配置数据
                config_data = self.protocol.decode_config_data(hex_data)
                self.config.set_all_configs(config_data)

                self.logger.info(f"成功获取汽车语音配置，共{len(config_data)}个功能")
                return True, config_data, "获取配置成功"

            else:
                return False, None, f"响应格式错误: {response}"
                
        except Exception as e:
            error_msg = f"获取汽车语音配置失败: {str(e)}"
            self.logger.error(error_msg)
            return False, None, error_msg
    
    def send_config_to_device(self, config_data: Optional[Dict[int, Dict[str, Any]]] = None) -> Tuple[bool, str]:
        """发送汽车语音配置到设备"""
        if not self.serial_comm:
            return False, "串口通信未初始化"
        
        try:
            # 使用传入的配置或当前配置
            if config_data is None:
                config_data = self.config.get_all_configs()
            
            # 验证配置
            is_valid, errors = self.protocol.validate_config(config_data)
            if not is_valid:
                return False, f"配置验证失败: {'; '.join(errors)}"
            
            self.logger.info("开始发送汽车语音配置...")
            
            # 编码配置数据
            hex_data = self.protocol.encode_config_data(config_data)
            
            # 构建命令
            command = f"SET_CAR_VOICE_CONFIG:{hex_data}"
            
            # 发送配置命令
            success, response = self.serial_comm.send_command(command)
            
            if success:
                self.logger.info("汽车语音配置发送成功")
                return True, "配置发送成功"
            else:
                return False, f"配置发送失败: {response}"
                
        except Exception as e:
            error_msg = f"发送汽车语音配置失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg
    
    def reset_config_to_default(self) -> Tuple[bool, str]:
        """重置配置为默认值"""
        try:
            if not self.serial_comm:
                return False, "串口通信未初始化"
            
            self.logger.info("开始重置汽车语音配置为默认值...")
            
            # 发送重置命令
            success, response = self.serial_comm.send_command("RESET_CAR_VOICE_CONFIG")
            
            if success:
                # 重置本地配置
                self.config.reset_to_default()
                self.logger.info("汽车语音配置重置成功")
                return True, "配置重置成功"
            else:
                return False, f"配置重置失败: {response}"
                
        except Exception as e:
            error_msg = f"重置汽车语音配置失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def test_voice_function(self, func_id: int) -> Tuple[bool, str]:
        """测试指定语音功能"""
        if not self.serial_comm:
            return False, "串口通信未初始化"

        try:
            if func_id not in range(1, 18):
                return False, f"无效的功能ID: {func_id}"

            self.logger.info(f"开始测试汽车语音功能{func_id}...")

            # 发送测试命令
            command = f"TEST_CAR_VOICE:{func_id}"
            success, response = self.serial_comm.send_command(command)

            if success:
                self.logger.info(f"汽车语音功能{func_id}测试成功")
                return True, f"功能{func_id}测试播放成功"
            else:
                return False, f"功能{func_id}测试失败: {response}"

        except Exception as e:
            error_msg = f"测试汽车语音功能{func_id}失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg

    def test_all_functions(self) -> Tuple[bool, List[str]]:
        """测试所有启用的语音功能"""
        if not self.serial_comm:
            return False, ["串口通信未初始化"]

        results = []
        success_count = 0

        for func_id in range(1, 18):
            if self.config.is_function_enabled(func_id):
                success, message = self.test_voice_function(func_id)
                results.append(f"功能{func_id}: {message}")
                if success:
                    success_count += 1

        if success_count == len([f for f in range(1, 18) if self.config.is_function_enabled(f)]):
            return True, results
        else:
            return False, results

    def get_function_info(self, func_id: int) -> Optional[Dict[str, Any]]:
        """获取功能信息"""
        return self.protocol.get_function_info(func_id)

    def get_all_functions_info(self) -> Dict[int, Dict[str, Any]]:
        """获取所有功能信息（严格按照文档1.3节定义）"""
        functions_info = {}

        # 根据文档1.3节定义的功能（合并4-7和8-11）
        function_definitions = {
            1: {"name": "ACC开启欢迎语", "params": ["delay_time"]},  # 延迟时间(3秒)
            2: {"name": "R档倒车提醒", "params": []},  # 无参数
            3: {"name": "D档起步提醒", "params": []},  # 无参数
            4: {"name": "车门开启提醒", "params": []},  # 4-7合并，无参数
            5: {"name": "车门未关警告", "params": ["speed_threshold"]},  # 8-11合并，车速阈值(10km/h)
            6: {"name": "停车未熄火提醒", "params": ["parking_time", "interval", "max_count"]},  # 停车时间(60分钟)、间隔(60分钟)、次数(3次)
            7: {"name": "方向盘预警", "params": ["speed_threshold", "angle_threshold", "duration"]},  # 车速(60km/h)、转角(15度)、持续(20秒)
            8: {"name": "疲劳驾驶提醒", "params": ["speed_threshold", "drive_time"]},  # 车速(30km/h)、时间(2小时)
            9: {"name": "熄火物品提醒", "params": []},  # 无参数
            10: {"name": "方向盘未回正", "params": ["angle_threshold"]},  # 转角阈值(10度)
            11: {"name": "转向灯持续过长", "params": ["speed_threshold", "duration", "interval", "max_count"]}  # 车速(30km/h)、持续(20秒)、间隔(10秒)、次数(3次)
        }

        for func_id in range(1, 12):  # 11个功能（合并后）
            config = self.config.get_config(func_id)
            definition = function_definitions.get(func_id, {"name": f"未知功能{func_id}", "params": []})

            functions_info[func_id] = {
                "name": definition["name"],
                "params": definition["params"],
                "config": config
            }

        return functions_info

    def get_local_config(self) -> Dict[int, Dict[str, Any]]:
        """获取本地配置"""
        return self.config.get_all_configs()

    def set_local_config(self, config_data: Dict[int, Dict[str, Any]]):
        """设置本地配置"""
        self.config.set_all_configs(config_data)

    def enable_function(self, func_id: int, enabled: bool = True):
        """启用/禁用指定功能"""
        self.config.enable_function(func_id, enabled)

    def is_function_enabled(self, func_id: int) -> bool:
        """检查功能是否启用"""
        return self.config.is_function_enabled(func_id)

    def export_config_to_json(self) -> str:
        """导出配置为JSON"""
        return self.config.export_to_json()

    def import_config_from_json(self, json_str: str):
        """从JSON导入配置"""
        self.config.import_from_json(json_str)

    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return self.config.get_function_summary()
