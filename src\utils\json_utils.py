#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON工具模块
提供JSON数据处理的便捷方法
"""

import json
import logging
from typing import Any, Dict, Optional

class JSONUtils:
    """JSON工具类"""
    
    @staticmethod
    def safe_loads(json_str: str) -> Optional[Dict]:
        """
        安全解析JSON字符串
        
        Args:
            json_str: JSON字符串
            
        Returns:
            Dict: 解析后的字典，失败返回None
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logging.getLogger(__name__).error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            logging.getLogger(__name__).error(f"JSON解析异常: {e}")
            return None
    
    @staticmethod
    def safe_dumps(data: Any, ensure_ascii: bool = False, indent: int = None) -> Optional[str]:
        """
        安全序列化为JSON字符串
        
        Args:
            data: 要序列化的数据
            ensure_ascii: 是否确保ASCII编码
            indent: 缩进空格数
            
        Returns:
            str: JSON字符串，失败返回None
        """
        try:
            return json.dumps(data, ensure_ascii=ensure_ascii, indent=indent)
        except TypeError as e:
            logging.getLogger(__name__).error(f"JSON序列化失败: {e}")
            return None
        except Exception as e:
            logging.getLogger(__name__).error(f"JSON序列化异常: {e}")
            return None
    
    @staticmethod
    def validate_command_request(data: Dict) -> bool:
        """
        验证命令请求格式
        
        Args:
            data: 请求数据
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(data, dict):
            return False
        
        # 必须包含cmd和id字段
        if 'cmd' not in data or 'id' not in data:
            return False
        
        # cmd必须是字符串
        if not isinstance(data['cmd'], str):
            return False
        
        # id必须是字符串
        if not isinstance(data['id'], str):
            return False
        
        return True
    
    @staticmethod
    def validate_command_response(data: Dict) -> bool:
        """
        验证命令响应格式
        
        Args:
            data: 响应数据
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(data, dict):
            return False
        
        # 必须包含status和id字段
        if 'status' not in data or 'id' not in data:
            return False
        
        # status必须是success或error
        if data['status'] not in ['success', 'error']:
            return False
        
        # id必须是字符串
        if not isinstance(data['id'], str):
            return False
        
        return True
    
    @staticmethod
    def create_command_request(cmd: str, request_id: str, data: Dict = None) -> Dict:
        """
        创建标准命令请求
        
        Args:
            cmd: 命令类型
            request_id: 请求ID
            data: 命令数据
            
        Returns:
            Dict: 命令请求
        """
        request = {
            "cmd": cmd,
            "id": request_id
        }
        
        if data:
            request["data"] = data
        
        return request
    
    @staticmethod
    def extract_response_data(response: Dict) -> Optional[Dict]:
        """
        提取响应数据
        
        Args:
            response: 响应字典
            
        Returns:
            Dict: 数据部分，失败返回None
        """
        if not JSONUtils.validate_command_response(response):
            return None
        
        if response.get('status') != 'success':
            return None
        
        return response.get('data', {})
    
    @staticmethod
    def get_error_message(response: Dict) -> str:
        """
        获取错误消息
        
        Args:
            response: 响应字典
            
        Returns:
            str: 错误消息
        """
        if not isinstance(response, dict):
            return "无效的响应格式"
        
        if response.get('status') == 'error':
            return response.get('message', '未知错误')
        
        return ""
