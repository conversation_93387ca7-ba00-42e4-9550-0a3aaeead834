#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汽车语音功能配置模型
作者: AI Assistant
版本: 1.0.0
"""

import json
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class CarVoiceFunctionConfig:
    """单个汽车语音功能配置"""
    enabled: bool = False
    cooldown: int = 30  # 冷却时间(秒)
    delay_play: int = 0  # 延迟播放(秒)
    max_count: int = 3  # 最大提醒次数
    fuel_threshold: int = 20  # 油量阈值(%)
    temp_threshold: int = 90  # 温度阈值(°C)
    pressure_threshold: int = 200  # 胎压阈值(kPa)
    speed_threshold: int = 120  # 速度阈值(km/h)
    angle_threshold: int = 15  # 角度阈值(度)
    duration: int = 20  # 持续时间(秒)
    drive_time_threshold: int = 120  # 驾驶时间阈值(分钟)
    acceleration_threshold: int = 5  # 加速度阈值(m/s²)
    deceleration_threshold: int = 8  # 减速度阈值(m/s²)
    start_time: str = "22:00"  # 开始时间
    end_time: str = "06:00"  # 结束时间
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CarVoiceFunctionConfig':
        """从字典创建配置"""
        return cls(**data)


class CarVoiceConfig:
    """汽车语音功能配置管理器"""
    
    # 默认配置（严格按照文档1.3节定义）
    DEFAULT_CONFIGS = {
        1: {  # ACC开启欢迎语
            "name": "ACC开启欢迎语",
            "enabled": True,
            "cooldown": 30,
            "delay_time": 3  # 延迟时间(3秒)
        },
        2: {  # R档倒车提醒
            "name": "R档倒车提醒",
            "enabled": True,
            "cooldown": 5
        },
        3: {  # D档起步提醒
            "name": "D档起步提醒",
            "enabled": True,
            "cooldown": 5
        },
        4: {  # 车门开启提醒
            "name": "车门开启提醒",
            "enabled": True,
            "cooldown": 10
        },
        5: {  # 车门开启提醒
            "name": "车门开启提醒",
            "enabled": True,
            "cooldown": 10
        },
        6: {  # 车门开启提醒
            "name": "车门开启提醒",
            "enabled": True,
            "cooldown": 10
        },
        7: {  # 车门开启提醒
            "name": "车门开启提醒",
            "enabled": True,
            "cooldown": 10
        },
        8: {  # 车门未关警告
            "name": "车门未关警告",
            "enabled": True,
            "cooldown": 30,
            "speed_threshold": 10  # 车速阈值(10km/h)
        },
        9: {  # 车门未关警告
            "name": "车门未关警告",
            "enabled": True,
            "cooldown": 30,
            "speed_threshold": 10
        },
        10: {  # 车门未关警告
            "name": "车门未关警告",
            "enabled": True,
            "cooldown": 30,
            "speed_threshold": 10
        },
        11: {  # 车门未关警告
            "name": "车门未关警告",
            "enabled": True,
            "cooldown": 30,
            "speed_threshold": 10
        },
        12: {  # 停车未熄火提醒
            "name": "停车未熄火提醒",
            "enabled": True,
            "cooldown": 60,
            "parking_time": 60,  # 停车时间(60分钟)
            "interval": 60,      # 间隔(60分钟)
            "max_count": 3       # 次数(3次)
        },
        13: {  # 方向盘预警
            "name": "方向盘预警",
            "enabled": True,
            "cooldown": 20,
            "speed_threshold": 60,    # 车速(60km/h)
            "angle_threshold": 15,    # 转角(15度)
            "duration": 20           # 持续(20秒)
        },
        14: {  # 疲劳驾驶提醒
            "name": "疲劳驾驶提醒",
            "enabled": True,
            "cooldown": 3600,
            "speed_threshold": 30,    # 车速(30km/h)
            "drive_time": 2          # 时间(2小时)
        },
        15: {  # 熄火物品提醒
            "name": "熄火物品提醒",
            "enabled": True,
            "cooldown": 30
        },
        16: {  # 方向盘未回正
            "name": "方向盘未回正",
            "enabled": True,
            "cooldown": 30,
            "angle_threshold": 10    # 转角阈值(10度)
        },
        17: {  # 转向灯持续过长
            "name": "转向灯持续过长",
            "enabled": True,
            "cooldown": 30,
            "speed_threshold": 30,   # 车速(30km/h)
            "duration": 20,          # 持续(20秒)
            "interval": 10,          # 间隔(10秒)
            "max_count": 3           # 次数(3次)
        }
    }
    
    def __init__(self):
        """初始化配置管理器"""
        self.logger = logging.getLogger(__name__)
        self.configs: Dict[int, Dict[str, Any]] = {}
        self.load_default_configs()
    
    def load_default_configs(self):
        """加载默认配置"""
        self.configs = {}
        for func_id, config in self.DEFAULT_CONFIGS.items():
            self.configs[func_id] = config.copy()
        self.logger.info("已加载默认汽车语音配置")
    
    def get_config(self, func_id: int) -> Optional[Dict[str, Any]]:
        """获取指定功能的配置"""
        return self.configs.get(func_id)
    
    def set_config(self, func_id: int, config: Dict[str, Any]):
        """设置指定功能的配置"""
        if func_id in range(1, 18):
            self.configs[func_id] = config.copy()
            self.logger.info(f"已更新功能{func_id}的配置")
        else:
            raise ValueError(f"无效的功能ID: {func_id}")
    
    def get_all_configs(self) -> Dict[int, Dict[str, Any]]:
        """获取所有配置"""
        return self.configs.copy()
    
    def set_all_configs(self, configs: Dict[int, Dict[str, Any]]):
        """设置所有配置"""
        self.configs = {}
        for func_id, config in configs.items():
            if func_id in range(1, 18):
                self.configs[func_id] = config.copy()
        self.logger.info(f"已更新所有汽车语音配置，共{len(self.configs)}个功能")
    
    def enable_function(self, func_id: int, enabled: bool = True):
        """启用/禁用指定功能"""
        if func_id in self.configs:
            self.configs[func_id]['enabled'] = enabled
            status = "启用" if enabled else "禁用"
            self.logger.info(f"已{status}功能{func_id}")
        else:
            raise ValueError(f"功能{func_id}不存在")
    
    def is_function_enabled(self, func_id: int) -> bool:
        """检查功能是否启用"""
        config = self.configs.get(func_id)
        return config.get('enabled', False) if config else False
    
    def reset_to_default(self, func_id: Optional[int] = None):
        """重置为默认配置"""
        if func_id is not None:
            if func_id in self.DEFAULT_CONFIGS:
                self.configs[func_id] = self.DEFAULT_CONFIGS[func_id].copy()
                self.logger.info(f"已重置功能{func_id}为默认配置")
            else:
                raise ValueError(f"无效的功能ID: {func_id}")
        else:
            self.load_default_configs()
            self.logger.info("已重置所有功能为默认配置")
    
    def export_to_json(self) -> str:
        """导出配置为JSON字符串"""
        try:
            return json.dumps(self.configs, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"导出配置失败: {str(e)}")
            raise
    
    def import_from_json(self, json_str: str):
        """从JSON字符串导入配置"""
        try:
            configs = json.loads(json_str)
            
            # 验证配置格式
            for func_id, config in configs.items():
                func_id = int(func_id)
                if func_id not in range(1, 18):
                    raise ValueError(f"无效的功能ID: {func_id}")
                if not isinstance(config, dict):
                    raise ValueError(f"功能{func_id}配置格式错误")
            
            # 应用配置
            self.set_all_configs({int(k): v for k, v in configs.items()})
            self.logger.info("从JSON导入配置成功")
            
        except Exception as e:
            self.logger.error(f"从JSON导入配置失败: {str(e)}")
            raise
    
    def validate_config(self, func_id: int, config: Dict[str, Any]) -> bool:
        """验证配置的有效性"""
        try:
            # 检查必需字段
            if 'enabled' not in config:
                return False
            
            # 检查数值范围
            for key, value in config.items():
                if key == 'enabled':
                    if not isinstance(value, bool):
                        return False
                elif key in ['start_time', 'end_time']:
                    if not isinstance(value, str) or ':' not in value:
                        return False
                    try:
                        hour, minute = map(int, value.split(':'))
                        if not (0 <= hour <= 23 and 0 <= minute <= 59):
                            return False
                    except ValueError:
                        return False
                elif isinstance(value, (int, float)):
                    if value < 0 or value > 0xFFFFFFFF:
                        return False
            
            return True
            
        except Exception:
            return False
    
    def get_function_summary(self) -> Dict[str, Any]:
        """获取功能配置摘要"""
        summary = {
            'total_functions': len(self.configs),
            'enabled_functions': sum(1 for config in self.configs.values() if config.get('enabled', False)),
            'disabled_functions': sum(1 for config in self.configs.values() if not config.get('enabled', False)),
            'functions': {}
        }
        
        for func_id, config in self.configs.items():
            summary['functions'][func_id] = {
                'enabled': config.get('enabled', False),
                'cooldown': config.get('cooldown', 0)
            }
        
        return summary
