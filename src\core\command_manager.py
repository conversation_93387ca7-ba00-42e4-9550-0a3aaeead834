#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指令管理模块
负责文本指令和系统指令的管理
使用简化文本协议进行通信
"""

import logging
import re
import time
from typing import Dict, Any, Optional
from core.serial_comm import SerialCommunication

class CommandManager:
    """指令管理类"""

    def __init__(self, serial_comm: SerialCommunication):
        self.serial_comm = serial_comm
        self.logger = logging.getLogger(__name__)
        self.text_commands: Dict[str, str] = {}  # 文本指令：名称->内容
        self.system_commands: Dict[str, Dict] = {}  # 系统指令：名称->详情
        self.system_status: Dict[str, Any] = {}

        # 指令限制
        self.MAX_COMMANDS = 50
        self.MAX_NAME_LENGTH = 32
        self.MAX_TEXT_LENGTH = 500

        # 错误消息映射
        self.ERROR_MESSAGES = {
            "command not found": "指令不存在",
            "invalid parameters": "参数无效",
            "command exists": "指令已存在",
            "storage full": "存储空间已满（最多50个指令）",
            "storage failed": "存储操作失败",
            "execution failed": "指令执行失败",
            "system command protected": "系统指令受保护",
            "invalid restore data": "恢复数据无效",
            "reset failed": "重置失败"
        }

    def validate_command_name(self, name: str) -> bool:
        """
        验证指令名称
        规则：只能包含字母、数字、下划线，不能以数字开头，长度不超过32字符
        """
        if not name or len(name) > self.MAX_NAME_LENGTH:
            return False

        # 不能以数字开头
        if name[0].isdigit():
            return False

        # 只能包含字母、数字、下划线
        pattern = r'^[a-zA-Z_][a-zA-Z0-9_]*$'
        return bool(re.match(pattern, name))

    def validate_command_text(self, text: str) -> bool:
        """验证指令文本"""
        return text and len(text.strip()) <= self.MAX_TEXT_LENGTH

    def translate_error_message(self, error_msg: str) -> str:
        """翻译错误消息"""
        if not error_msg:
            return "未知错误"

        # 查找匹配的错误消息
        for eng_msg, chn_msg in self.ERROR_MESSAGES.items():
            if eng_msg.lower() in error_msg.lower():
                return chn_msg

        # 如果没有找到匹配的，返回原始消息
        return error_msg
    
    def get_text_commands(self) -> Optional[Dict[str, str]]:
        """获取文本指令列表"""
        try:
            self.logger.info("正在获取文本指令列表...")
            response = self.serial_comm.send_command("LIST_TEXT")
            if response and response.get("status") == "success":
                data = response.get("data", "")
                self.text_commands = self._parse_command_list(data)
                self.logger.info(f"成功获取到 {len(self.text_commands)} 个文本指令")
                if self.text_commands:
                    cmd_names = list(self.text_commands.keys())[:5]  # 显示前5个
                    self.logger.info(f"指令列表: {cmd_names}{'...' if len(self.text_commands) > 5 else ''}")
                return self.text_commands
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取文本指令失败: {error_msg}")
                return None
        except Exception as e:
            self.logger.error(f"获取文本指令异常: {e}")
            return None

    def _parse_command_list(self, data: str) -> Dict[str, str]:
        """解析指令列表数据"""
        commands = {}
        if not data:
            self.logger.info("文本指令数据为空")
            return commands

        self.logger.info(f"开始解析文本指令数据: '{data}'")

        # 格式：name1=text1,name2=text2
        try:
            pairs = data.split(',')
            self.logger.info(f"分割后得到 {len(pairs)} 个部分: {pairs}")

            for pair in pairs:
                pair = pair.strip()
                if '=' in pair:
                    name, text = pair.split('=', 1)
                    name = name.strip()
                    text = text.strip()
                    commands[name] = text
                    self.logger.info(f"解析到文本指令: {name} = {text}")
                else:
                    # 如果没有等号，可能是只有名称的格式（类似系统指令）
                    if pair:
                        self.logger.warning(f"文本指令格式异常，没有等号: '{pair}'")
                        # 可以选择跳过或者作为空内容处理
                        # commands[pair] = ""  # 如果要保留的话

        except Exception as e:
            self.logger.error(f"解析指令列表失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

        self.logger.info(f"文本指令解析完成，共解析到 {len(commands)} 个指令: {list(commands.keys())}")
        return commands
    
    def get_system_commands(self) -> Optional[Dict[str, Dict]]:
        """获取系统指令列表"""
        try:
            self.logger.info("正在获取系统指令列表...")
            response = self.serial_comm.send_command("LIST_SYS")
            if response and response.get("status") == "success":
                data = response.get("data", "")
                self.system_commands = self._parse_system_commands(data)
                self.logger.info(f"成功获取到 {len(self.system_commands)} 个系统指令")
                if self.system_commands:
                    cmd_names = list(self.system_commands.keys())
                    self.logger.info(f"系统指令: {cmd_names}")
                return self.system_commands
            elif response and response.get("status") == "unknown":
                # 处理unknown状态的响应，可能是系统指令数据
                raw_data = response.get("raw", "")
                if raw_data and ("help" in raw_data or "wifi" in raw_data or "reboot" in raw_data):
                    # 看起来是系统指令数据，尝试解析
                    self.logger.info(f"检测到可能的系统指令数据: {raw_data}")
                    # 如果是"K:xxx"格式，提取数据部分
                    if raw_data.startswith("K:"):
                        data = raw_data[2:]
                    else:
                        data = raw_data
                    self.system_commands = self._parse_system_commands(data)
                    self.logger.info(f"成功解析到 {len(self.system_commands)} 个系统指令")
                    if self.system_commands:
                        cmd_names = list(self.system_commands.keys())
                        self.logger.info(f"系统指令: {cmd_names}")
                    return self.system_commands
                else:
                    self.logger.error(f"获取系统指令失败: 未知响应格式 - {raw_data}")
                    return None
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取系统指令失败: {error_msg}")
                return None
        except Exception as e:
            self.logger.error(f"获取系统指令异常: {e}")
            return None

    def _parse_system_commands(self, data: str) -> Dict[str, Dict]:
        """解析系统指令数据"""
        commands = {}

        # 系统指令的协议格式映射
        protocol_mapping = {
            "help": "GET_SYS:help",
            "wifi": "GET_SYS:wifi",
            "4g": "GET_SYS:4g",
            "reboot": "GET_SYS:reboot",
            "ota": "GET_SYS:ota",
            "settings": "GET_SYS:settings"
        }

        if not data:
            return commands

        # 格式：name1,name2,name3
        try:
            names = data.split(',')
            for name in names:
                name = name.strip()
                if name:
                    protocol = protocol_mapping.get(name, f"GET_SYS:{name}")
                    commands[name] = {
                        "name": name,
                        "description": f"获取{name}命令描述",
                        "protocol": protocol,
                        "type": "system",
                        "readonly": True
                    }
        except Exception as e:
            self.logger.error(f"解析系统指令失败: {e}")

        # 添加额外的协议命令
        extra_commands = {
            "LIST_SYS": {
                "name": "LIST_SYS",
                "description": "获取所有系统命令列表",
                "protocol": "LIST_SYS",
                "type": "protocol",
                "readonly": True
            },
            "STATUS": {
                "name": "STATUS",
                "description": "获取系统状态信息",
                "protocol": "STATUS",
                "type": "protocol",
                "readonly": True
            },
            "PING": {
                "name": "PING",
                "description": "测试连接",
                "protocol": "PING",
                "type": "protocol",
                "readonly": True
            }
        }

        # 合并额外命令
        commands.update(extra_commands)

        return commands
    
    def get_system_status(self) -> Optional[Dict[str, Any]]:
        """获取系统状态"""
        try:
            self.logger.info("正在获取系统状态...")
            response = self.serial_comm.send_command("STATUS")
            if response and response.get("status") == "success":
                data = response.get("data", "")
                self.system_status = self._parse_system_status(data)
                self.logger.info(f"系统状态获取成功: {self.system_status}")
                return self.system_status
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取系统状态失败: {error_msg}")
                return None
        except Exception as e:
            self.logger.error(f"获取系统状态异常: {e}")
            return None

    def _parse_system_status(self, data: str) -> Dict[str, Any]:
        """解析系统状态数据"""
        status = {}
        if not data:
            return status

        # 格式：connected,sys_cmds:13,text_cmds:2,storage:ok,free_mem:95183
        try:
            parts = data.split(',')
            for part in parts:
                part = part.strip()
                if ':' in part:
                    key, value = part.split(':', 1)
                    key = key.strip()
                    value = value.strip()

                    # 尝试转换为数字
                    if value.isdigit():
                        status[key] = int(value)
                    else:
                        status[key] = value
                else:
                    # 简单状态值（如connected）
                    status[part] = True
        except Exception as e:
            self.logger.error(f"解析系统状态失败: {e}")

        return status
    
    def add_command(self, name: str, text: str) -> bool:
        """
        添加文本指令

        Args:
            name: 指令名称
            text: 指令文本内容

        Returns:
            bool: 是否添加成功
        """
        try:
            # 验证输入
            if not self.validate_command_name(name):
                self.logger.error(f"指令名称无效: {name}")
                return False

            if not self.validate_command_text(text):
                self.logger.error(f"指令文本无效: {text}")
                return False

            # 检查数量限制
            if len(self.text_commands) >= self.MAX_COMMANDS:
                self.logger.error(f"指令数量已达上限: {self.MAX_COMMANDS}")
                return False

            # 检查名称是否已存在
            if name in self.text_commands:
                self.logger.error(f"指令名称已存在: {name}")
                return False

            response = self.serial_comm.send_command("ADD", name, text)
            if response and response.get("status") == "success":
                self.logger.info(f"添加指令成功: {name}")
                # 不在这里刷新数据，让GUI负责刷新以避免重复调用
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"添加指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"添加指令异常: {e}")
            return False
    
    def modify_command(self, name: str, new_text: str) -> bool:
        """
        修改文本指令

        Args:
            name: 指令名称
            new_text: 新的指令文本

        Returns:
            bool: 是否修改成功
        """
        try:
            # 验证输入
            if not self.validate_command_name(name):
                self.logger.error(f"指令名称无效: {name}")
                return False

            if not self.validate_command_text(new_text):
                self.logger.error(f"指令文本无效: {new_text}")
                return False

            # 检查指令是否存在
            if name not in self.text_commands:
                self.logger.error(f"指令不存在: {name}")
                return False

            response = self.serial_comm.send_command("MOD", name, new_text)
            if response and response.get("status") == "success":
                self.logger.info(f"修改指令成功: {name}")
                # 不在这里刷新数据，让GUI负责刷新以避免重复调用
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"修改指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"修改指令异常: {e}")
            return False
    
    def delete_command(self, name: str) -> bool:
        """
        删除文本指令

        Args:
            name: 指令名称

        Returns:
            bool: 是否删除成功
        """
        try:
            # 验证指令名称
            if not self.validate_command_name(name):
                self.logger.error(f"指令名称无效: {name}")
                return False

            # 检查指令是否存在
            if name not in self.text_commands:
                self.logger.error(f"指令不存在: {name}")
                return False

            response = self.serial_comm.send_command("DEL", name)
            if response and response.get("status") == "success":
                self.logger.info(f"删除指令成功: {name}")
                # 不在这里刷新数据，让GUI负责刷新以避免重复调用
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"删除指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"删除指令异常: {e}")
            return False
    
    def execute_command(self, name: str) -> bool:
        """
        执行指令

        Args:
            name: 指令名称

        Returns:
            bool: 是否执行成功
        """
        try:
            # 验证指令名称
            if not self.validate_command_name(name):
                self.logger.error(f"指令名称无效: {name}")
                return False

            response = self.serial_comm.send_command("EXEC", name)
            if response and response.get("status") == "success":
                self.logger.info(f"执行指令成功: {name}")
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"执行指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"执行指令异常: {e}")
            return False
    
    def backup_commands(self) -> Optional[Dict]:
        """
        备份所有文本指令

        Returns:
            Dict: 备份数据，失败返回None
        """
        try:
            response = self.serial_comm.send_command("BACKUP")
            if response and response.get("status") == "success":
                data = response.get("data", "")
                backup_data = {
                    "text_commands": self._parse_command_list(data),
                    "backup_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "version": "1.0"
                }
                self.logger.info("备份指令成功")
                return backup_data
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"备份指令失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"备份指令异常: {e}")
            return None
    
    def restore_commands(self, backup_data: Dict) -> bool:
        """
        恢复文本指令

        Args:
            backup_data: 备份数据

        Returns:
            bool: 是否恢复成功
        """
        try:
            text_commands = backup_data.get("text_commands", {})
            if not text_commands:
                self.logger.error("备份数据中没有文本指令")
                return False

            # 构建恢复数据字符串
            restore_data = []
            for name, text in text_commands.items():
                restore_data.append(f"{name}={text}")
            data_str = ",".join(restore_data)

            response = self.serial_comm.send_command("RESTORE", data_str)
            if response and response.get("status") == "success":
                self.logger.info("恢复指令成功")
                # 刷新本地缓存
                self.text_commands = text_commands.copy()
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"恢复指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"恢复指令异常: {e}")
            return False

    def reset_commands(self) -> bool:
        """
        重置所有文本指令

        Returns:
            bool: 是否重置成功
        """
        try:
            response = self.serial_comm.send_command("RESET")
            if response and response.get("status") == "success":
                self.logger.info("重置指令成功")
                # 清空本地缓存
                self.text_commands.clear()
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"重置指令失败: {error_msg}")
                return False

        except Exception as e:
            self.logger.error(f"重置指令异常: {e}")
            return False
    
    def get_text_command_detail(self, name: str) -> Optional[str]:
        """获取文本指令详情"""
        try:
            response = self.serial_comm.send_command("GET_TEXT", name)
            if response and response.get("status") == "success":
                text = response.get("data", "")
                self.logger.info(f"获取指令详情成功: {name}")
                return text
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取指令详情失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"获取指令详情异常: {e}")
            return None

    def get_system_command_detail(self, name: str) -> Optional[Dict]:
        """获取系统指令详情"""
        try:
            response = self.serial_comm.send_command("GET_SYS", name)
            if response and response.get("status") == "success":
                data = response.get("data", "")
                # 解析系统指令详情：格式为 name=description
                description = ""
                if '=' in data:
                    cmd_name, desc = data.split('=', 1)
                    if cmd_name.strip() == name:
                        description = desc.strip()
                else:
                    description = data

                detail = {
                    "name": name,
                    "description": description,
                    "type": "system"
                }
                self.logger.info(f"获取系统指令详情成功: {name}")
                return detail
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取系统指令详情失败: {error_msg}")
                return None

        except Exception as e:
            self.logger.error(f"获取系统指令详情异常: {e}")
            return None

    def switch_to_command_mode(self) -> bool:
        """切换到指令管理模式"""
        try:
            response = self.serial_comm.send_command("cmd_gpio")
            if response and response.get("status") == "success":
                self.logger.info("切换到指令管理模式成功")
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"切换到指令管理模式失败: {error_msg}")
                return False
        except Exception as e:
            self.logger.error(f"切换到指令管理模式异常: {e}")
            return False

    def switch_to_car_mode(self) -> bool:
        """切换到汽车语音模式"""
        try:
            response = self.serial_comm.send_command("car_gpio")
            if response and response.get("status") == "success":
                self.logger.info("切换到汽车语音模式成功")
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"切换到汽车语音模式失败: {error_msg}")
                return False
        except Exception as e:
            self.logger.error(f"切换到汽车语音模式异常: {e}")
            return False

    def get_gpio_status(self) -> Optional[Dict]:
        """获取GPIO状态"""
        try:
            response = self.serial_comm.send_command("gpio_status")
            if response and response.get("status") == "success":
                data = response.get("data", "")
                # 解析GPIO状态：格式可能为 mode:cmd,gpio:17/19,baudrate:115200
                status = {}
                parts = data.split(',')
                for part in parts:
                    if ':' in part:
                        key, value = part.split(':', 1)
                        status[key.strip()] = value.strip()

                self.logger.info("获取GPIO状态成功")
                return status
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"获取GPIO状态失败: {error_msg}")
                return None
        except Exception as e:
            self.logger.error(f"获取GPIO状态异常: {e}")
            return None

    def create_reminder(self, reminder_data: Dict[str, Any]) -> bool:
        """
        创建提醒

        Args:
            reminder_data: 提醒数据，包含title、type等字段

        Returns:
            bool: 是否创建成功
        """
        try:
            self.logger.info(f"正在创建提醒: {reminder_data}")

            # 构建提醒指令
            title = reminder_data.get("title", "")
            reminder_type = reminder_data.get("type", "once")

            if not title:
                self.logger.error("提醒标题不能为空")
                return False

            # 根据文档，使用统一的REMINDER_ADD指令格式
            # 格式：REMINDER_ADD "提醒标题" "时间" "类型"

            if reminder_type == "once":
                # 一次性提醒
                if "minutes" in reminder_data:
                    # 分钟后的提醒，需要计算具体时间
                    from datetime import datetime, timedelta
                    minutes = reminder_data["minutes"]
                    target_time = datetime.now() + timedelta(minutes=minutes)
                    time_str = target_time.strftime("%Y-%m-%d %H:%M:%S")
                    command = f'REMINDER_ADD "{title}" "{time_str}" "once"'
                elif "datetime" in reminder_data:
                    datetime_str = reminder_data["datetime"]
                    command = f'REMINDER_ADD "{title}" "{datetime_str}" "once"'
                else:
                    self.logger.error("一次性提醒缺少时间参数")
                    return False
            elif reminder_type == "daily":
                # 每日提醒
                if "time" in reminder_data:
                    # 对于每日提醒，使用今天的日期+指定时间
                    from datetime import datetime
                    time_str = reminder_data["time"]
                    today = datetime.now().strftime("%Y-%m-%d")
                    full_time = f"{today} {time_str}"
                    command = f'REMINDER_ADD "{title}" "{full_time}" "daily"'
                elif "datetime" in reminder_data:
                    datetime_str = reminder_data["datetime"]
                    command = f'REMINDER_ADD "{title}" "{datetime_str}" "daily"'
                else:
                    self.logger.error("每日提醒缺少时间参数")
                    return False
            elif reminder_type == "weekly":
                # 每周提醒
                if "weekday" in reminder_data and "time" in reminder_data:
                    # 每周提醒需要指定周几和时间
                    weekday = reminder_data["weekday"]
                    time_str = reminder_data["time"]
                    from datetime import datetime
                    today = datetime.now().strftime("%Y-%m-%d")
                    full_time = f"{today} {time_str}"
                    # 在指令中包含周几信息
                    command = f'REMINDER_ADD "{title}" "{full_time}" "weekly" {weekday}'
                elif "time" in reminder_data:
                    # 兼容旧格式
                    from datetime import datetime
                    time_str = reminder_data["time"]
                    today = datetime.now().strftime("%Y-%m-%d")
                    full_time = f"{today} {time_str}"
                    command = f'REMINDER_ADD "{title}" "{full_time}" "weekly"'
                elif "datetime" in reminder_data:
                    datetime_str = reminder_data["datetime"]
                    command = f'REMINDER_ADD "{title}" "{datetime_str}" "weekly"'
                else:
                    self.logger.error("每周提醒缺少时间参数")
                    return False
            elif reminder_type == "monthly":
                # 每月提醒
                if "monthday" in reminder_data and "time" in reminder_data:
                    # 每月提醒需要指定日期和时间
                    monthday = reminder_data["monthday"]
                    time_str = reminder_data["time"]
                    from datetime import datetime
                    today = datetime.now().strftime("%Y-%m-%d")
                    full_time = f"{today} {time_str}"
                    # 在指令中包含日期信息
                    command = f'REMINDER_ADD "{title}" "{full_time}" "monthly" {monthday}'
                else:
                    self.logger.error("每月提醒缺少时间参数")
                    return False
            else:
                self.logger.error(f"不支持的提醒类型: {reminder_type}")
                return False

            # 发送指令
            self.logger.info(f"发送提醒创建指令: {command}")
            response = self.serial_comm.send_command(command)

            if response and response.get("status") == "success":
                self.logger.info(f"提醒创建成功: {title}")
                return True
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                translated_error = self.translate_error_message(error_msg)
                self.logger.error(f"提醒创建失败: {translated_error}")
                return False

        except Exception as e:
            self.logger.error(f"创建提醒异常: {e}")
            return False
