#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汽车语音功能协议处理器
作者: AI Assistant
版本: 1.0.0
"""

import struct
import logging
from typing import Dict, List, Optional, Tuple, Any


class CarVoiceProtocol:
    """汽车语音功能协议处理器"""
    
    # 魔数
    MAGIC_NUMBER = 0x5A5A
    
    # 语音功能定义
    VOICE_FUNCTIONS = {
        1: {"name": "ACC开启欢迎语", "params": ["cooldown", "delay_play"]},
        2: {"name": "R档倒车提醒", "params": ["cooldown"]},
        3: {"name": "P档停车提醒", "params": ["cooldown"]},
        4: {"name": "安全带未系提醒", "params": ["cooldown", "max_count"]},
        5: {"name": "车门未关提醒", "params": ["cooldown", "max_count"]},
        6: {"name": "手刹未松提醒", "params": ["cooldown", "max_count"]},
        7: {"name": "大灯未关提醒", "params": ["cooldown", "max_count"]},
        8: {"name": "转向灯未关提醒", "params": ["cooldown", "max_count"]},
        9: {"name": "油量不足提醒", "params": ["cooldown", "fuel_threshold"]},
        10: {"name": "水温过高提醒", "params": ["cooldown", "temp_threshold"]},
        11: {"name": "胎压异常提醒", "params": ["cooldown", "pressure_threshold"]},
        12: {"name": "超速提醒", "params": ["cooldown", "speed_threshold"]},
        13: {"name": "方向盘预警", "params": ["cooldown", "speed_threshold", "angle_threshold", "duration"]},
        14: {"name": "疲劳驾驶提醒", "params": ["cooldown", "drive_time_threshold"]},
        15: {"name": "急加速提醒", "params": ["cooldown", "acceleration_threshold"]},
        16: {"name": "急刹车提醒", "params": ["cooldown", "deceleration_threshold"]},
        17: {"name": "夜间行车提醒", "params": ["cooldown", "start_time", "end_time"]}
    }
    
    def __init__(self):
        """初始化协议处理器"""
        self.logger = logging.getLogger(__name__)
        
    def calculate_crc16(self, data: bytes) -> int:
        """计算CRC16校验码"""
        crc = 0xFFFF
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 1:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        return crc & 0xFFFF
    
    def encode_config_data(self, config_data: Dict[int, Dict[str, Any]]) -> str:
        """将配置数据编码为十六进制字符串"""
        try:
            # 构建二进制数据
            binary_data = bytearray()
            
            # 添加魔数 (2字节)
            binary_data.extend(struct.pack('<H', self.MAGIC_NUMBER))
            
            # 添加数据长度占位符 (2字节)
            length_pos = len(binary_data)
            binary_data.extend(b'\x00\x00')
            
            # 添加配置数据
            for func_id in range(1, 18):  # 17个功能
                if func_id in config_data:
                    func_config = config_data[func_id]
                    # 启用标志 (1字节)
                    binary_data.append(1 if func_config.get('enabled', False) else 0)
                    
                    # 参数数据 (根据功能类型添加不同参数)
                    if func_id in self.VOICE_FUNCTIONS:
                        params = self.VOICE_FUNCTIONS[func_id]["params"]
                        for param in params:
                            value = func_config.get(param, 0)
                            if isinstance(value, str) and ':' in value:  # 时间格式 HH:MM
                                hour, minute = map(int, value.split(':'))
                                binary_data.extend(struct.pack('<HH', hour, minute))
                            else:
                                binary_data.extend(struct.pack('<I', int(value)))
                else:
                    # 功能未配置，添加默认值
                    binary_data.append(0)  # 禁用
                    if func_id in self.VOICE_FUNCTIONS:
                        params = self.VOICE_FUNCTIONS[func_id]["params"]
                        for param in params:
                            if param in ['start_time', 'end_time']:
                                binary_data.extend(struct.pack('<HH', 0, 0))
                            else:
                                binary_data.extend(struct.pack('<I', 0))
            
            # 更新数据长度
            data_length = len(binary_data) - 4  # 不包括魔数和长度字段
            struct.pack_into('<H', binary_data, length_pos, data_length)
            
            # 计算CRC16
            crc = self.calculate_crc16(binary_data)
            binary_data.extend(struct.pack('<H', crc))
            
            # 转换为十六进制字符串
            hex_string = binary_data.hex().upper()
            
            self.logger.info(f"编码配置数据成功，长度: {len(binary_data)} 字节")
            return hex_string
            
        except Exception as e:
            self.logger.error(f"编码配置数据失败: {str(e)}")
            raise
    
    def decode_config_data(self, hex_string: str) -> Dict[int, Dict[str, Any]]:
        """将十六进制字符串解码为配置数据"""
        try:
            # 转换为二进制数据
            binary_data = bytes.fromhex(hex_string)
            
            if len(binary_data) < 6:
                raise ValueError("数据长度不足")
            
            # 解析魔数
            magic = struct.unpack('<H', binary_data[0:2])[0]
            if magic != self.MAGIC_NUMBER:
                raise ValueError(f"魔数错误: {magic:04X}, 期望: {self.MAGIC_NUMBER:04X}")
            
            # 解析数据长度
            data_length = struct.unpack('<H', binary_data[2:4])[0]
            
            # 验证CRC16
            data_part = binary_data[:-2]  # 不包括CRC
            expected_crc = struct.unpack('<H', binary_data[-2:])[0]
            actual_crc = self.calculate_crc16(data_part)
            
            if expected_crc != actual_crc:
                raise ValueError(f"CRC校验失败: {actual_crc:04X}, 期望: {expected_crc:04X}")
            
            # 解析配置数据
            config_data = {}
            offset = 4  # 跳过魔数和长度
            
            for func_id in range(1, 18):
                if offset >= len(binary_data) - 2:  # 减去CRC长度
                    break
                
                # 读取启用标志
                enabled = binary_data[offset] != 0
                offset += 1
                
                func_config = {'enabled': enabled}
                
                # 读取参数
                if func_id in self.VOICE_FUNCTIONS:
                    params = self.VOICE_FUNCTIONS[func_id]["params"]
                    for param in params:
                        if offset + 4 > len(binary_data) - 2:
                            break
                        
                        if param in ['start_time', 'end_time']:
                            # 时间参数 (2个uint16)
                            hour = struct.unpack('<H', binary_data[offset:offset+2])[0]
                            minute = struct.unpack('<H', binary_data[offset+2:offset+4])[0]
                            func_config[param] = f"{hour:02d}:{minute:02d}"
                            offset += 4
                        else:
                            # 普通参数 (uint32)
                            value = struct.unpack('<I', binary_data[offset:offset+4])[0]
                            func_config[param] = value
                            offset += 4
                
                config_data[func_id] = func_config
            
            self.logger.info(f"解码配置数据成功，共 {len(config_data)} 个功能")
            return config_data
            
        except Exception as e:
            self.logger.error(f"解码配置数据失败: {str(e)}")
            raise
    
    def get_function_info(self, func_id: int) -> Optional[Dict[str, Any]]:
        """获取功能信息"""
        return self.VOICE_FUNCTIONS.get(func_id)
    
    def get_all_functions(self) -> Dict[int, Dict[str, Any]]:
        """获取所有功能信息"""
        return self.VOICE_FUNCTIONS.copy()
    
    def validate_config(self, config_data: Dict[int, Dict[str, Any]]) -> Tuple[bool, List[str]]:
        """验证配置数据"""
        errors = []
        
        for func_id, func_config in config_data.items():
            if func_id not in self.VOICE_FUNCTIONS:
                errors.append(f"未知功能ID: {func_id}")
                continue
            
            func_info = self.VOICE_FUNCTIONS[func_id]
            
            # 验证参数
            for param in func_info["params"]:
                if param not in func_config:
                    errors.append(f"功能{func_id}缺少参数: {param}")
                    continue
                
                value = func_config[param]
                
                # 验证时间格式
                if param in ['start_time', 'end_time']:
                    if not isinstance(value, str) or ':' not in value:
                        errors.append(f"功能{func_id}参数{param}格式错误，应为HH:MM")
                        continue
                    try:
                        hour, minute = map(int, value.split(':'))
                        if not (0 <= hour <= 23 and 0 <= minute <= 59):
                            errors.append(f"功能{func_id}参数{param}时间范围错误")
                    except ValueError:
                        errors.append(f"功能{func_id}参数{param}时间格式错误")
                
                # 验证数值范围
                elif isinstance(value, (int, float)):
                    if value < 0:
                        errors.append(f"功能{func_id}参数{param}不能为负数")
                    elif value > 0xFFFFFFFF:
                        errors.append(f"功能{func_id}参数{param}超出范围")
        
        return len(errors) == 0, errors
