#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
汽车语音设置选项卡
作者: AI Assistant
版本: 1.0.0
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from typing import Dict, Any, Optional, Callable
from core.car_voice.car_voice_manager import CarVoiceManager
from core.serial_comm import SerialCommunication


class CarVoiceTab:
    """汽车语音设置选项卡"""
    
    def __init__(self, parent: ttk.Notebook, serial_comm: Optional[SerialCommunication] = None):
        """初始化汽车语音选项卡"""
        self.logger = logging.getLogger(__name__)
        self.parent = parent
        self.serial_comm = serial_comm
        
        # 创建汽车语音管理器
        self.car_voice_manager = CarVoiceManager(serial_comm)
        
        # 创建选项卡框架
        self.frame = ttk.Frame(parent)
        parent.add(self.frame, text="🚗 汽车语音设置")
        
        # 功能控件字典
        self.function_widgets: Dict[int, Dict[str, tk.Widget]] = {}
        
        # 状态变量
        self.is_connected = False
        self.config_modified = False
        
        # 创建UI
        self.create_ui()
        
        # 绑定事件
        self.bind_events()
        
        self.logger.info("汽车语音选项卡初始化完成")
    
    def create_ui(self):
        """创建用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建顶部控制面板
        self.create_control_panel(main_frame)
        
        # 创建分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=(10, 5))
        
        # 创建功能配置区域
        self.create_functions_area(main_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 连接状态
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(side=tk.LEFT)
        
        ttk.Label(status_frame, text="连接状态:").pack(side=tk.LEFT)
        self.status_label = ttk.Label(status_frame, text="●未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.RIGHT)
        
        self.read_config_btn = ttk.Button(button_frame, text="读取配置", command=self.read_config)
        self.read_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.save_config_btn = ttk.Button(button_frame, text="保存配置", command=self.save_config)
        self.save_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.reset_config_btn = ttk.Button(button_frame, text="重置默认", command=self.reset_config)
        self.reset_config_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.test_all_btn = ttk.Button(button_frame, text="测试全部", command=self.test_all_functions)
        self.test_all_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # 导入导出按钮
        io_frame = ttk.Frame(control_frame)
        io_frame.pack(side=tk.RIGHT, padx=(20, 0))
        
        self.export_btn = ttk.Button(io_frame, text="导出配置", command=self.export_config)
        self.export_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.import_btn = ttk.Button(io_frame, text="导入配置", command=self.import_config)
        self.import_btn.pack(side=tk.LEFT)
    
    def create_functions_area(self, parent):
        """创建功能配置区域"""
        # 创建滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建功能配置项
        self.create_function_items(scrollable_frame)
        
        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def create_function_items(self, parent):
        """创建功能配置项"""
        functions_info = self.car_voice_manager.get_all_functions_info()
        
        for func_id, func_info in functions_info.items():
            self.create_function_item(parent, func_id, func_info)
    
    def create_function_item(self, parent, func_id: int, func_info: Dict[str, Any]):
        """创建单个功能配置项 - 紧凑单行布局"""
        # 主框架 - 添加分割线（功能标题向左移动60px）
        main_frame = ttk.Frame(parent)
        main_frame.pack(fill=tk.X, padx=(0, 5), pady=1)  # 左侧无边距

        # 分割线（除了第一个功能）
        if func_id > 1:
            separator = ttk.Separator(main_frame, orient='horizontal')
            separator.pack(fill=tk.X, pady=(2, 5))

        # 单行布局（整体向左移动50px，功能标题再向左移动60px）
        row_frame = ttk.Frame(main_frame)
        row_frame.pack(fill=tk.X, padx=(0, 5), pady=3)  # 左侧无边距，整体向左移动

        # 左侧：功能标题（通过调整宽度和位置实现向左移动60px的效果）
        title_label = ttk.Label(row_frame, text=f"{func_id:03d}-{func_info['name']}:",
                               width=15, anchor="w")  # 减少宽度，让后续元素向左移动
        title_label.pack(side=tk.LEFT, padx=(0, 5))  # 减少右侧间距

        # 启用开关（整体向左移动50px）
        enabled_var = tk.BooleanVar()
        enabled_check = ttk.Checkbutton(row_frame, text="启用/关闭", variable=enabled_var,
                                       command=lambda: self.on_function_enabled_changed(func_id))
        enabled_check.pack(side=tk.LEFT, padx=(0, 20))  # 紧贴开关，增加右侧间距

        # 中间：参数区域
        params_frame = ttk.Frame(row_frame)
        params_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # 右侧：按钮区域
        button_frame = ttk.Frame(row_frame)
        button_frame.pack(side=tk.RIGHT, padx=(10, 0))

        test_btn = ttk.Button(button_frame, text="测试", width=8,
                             command=lambda: self.test_function(func_id))
        test_btn.pack(side=tk.LEFT, padx=(0, 5))

        reset_btn = ttk.Button(button_frame, text="重置", width=8,
                              command=lambda: self.reset_function(func_id))
        reset_btn.pack(side=tk.LEFT)

        # 存储控件引用
        widgets = {
            'main_frame': main_frame,
            'enabled_var': enabled_var,
            'enabled_check': enabled_check,
            'params_frame': params_frame,
            'test_btn': test_btn,
            'reset_btn': reset_btn,
            'param_widgets': {}
        }

        # 创建参数控件
        self.create_parameter_widgets(params_frame, func_id, func_info['params'], widgets)

        self.function_widgets[func_id] = widgets
    
    def create_parameter_widgets(self, parent, func_id: int, params: list, widgets: Dict[str, Any]):
        """创建参数控件"""
        param_widgets = widgets['param_widgets']
        
        # 在同一行显示所有参数
        for i, param in enumerate(params):
            # 参数标签
            label_text = self.get_param_label(param)
            ttk.Label(parent, text=f"{label_text}:").pack(side=tk.LEFT, padx=(0, 5))

            # 参数输入控件
            if param in ['start_time', 'end_time']:
                # 时间选择
                time_var = tk.StringVar(value="00:00")
                time_entry = ttk.Entry(parent, textvariable=time_var, width=8)
                time_entry.pack(side=tk.LEFT, padx=(0, 15))
                param_widgets[param] = time_var
            else:
                # 数值输入 - 根据参数类型调整宽度
                value_var = tk.IntVar(value=0)

                # 根据参数名称设置合适的宽度（30px约等于4个字符）
                if 'delay' in param or 'interval' in param:
                    width = 4  # 秒数输入框，30px宽度
                elif 'threshold' in param or 'duration' in param:
                    width = 6  # 阈值和时长输入框稍宽一些
                else:
                    width = 4  # 默认宽度30px

                value_spinbox = ttk.Spinbox(parent, from_=0, to=9999,
                                          textvariable=value_var, width=width)
                value_spinbox.pack(side=tk.LEFT, padx=(0, 5))

                # 单位标签（根据功能ID和参数名称确定单位）
                unit_text = self.get_param_unit_by_function(func_id, param)
                if unit_text:
                    ttk.Label(parent, text=unit_text).pack(side=tk.LEFT, padx=(0, 15))
                else:
                    # 如果没有单位，添加一些间距
                    ttk.Label(parent, text="").pack(side=tk.LEFT, padx=(0, 10))

                param_widgets[param] = value_var

            # 绑定变化事件
            param_widgets[param].trace('w', lambda *args, fid=func_id: self.on_config_changed(fid))
    
    def get_param_label(self, param: str) -> str:
        """获取参数标签"""
        labels = {
            'delay_time': '延迟时间',           # ACC开启欢迎语：延迟时间(3秒)
            'speed_threshold': '车速阈值',      # 车门未关警告：车速阈值(10km/h)
            'parking_time': '停车时间',        # 停车未熄火提醒：停车时间(60分钟)
            'interval': '间隔',               # 停车未熄火提醒：间隔(60分钟)、转向灯持续过长：间隔(10秒)
            'max_count': '次数',              # 停车未熄火提醒：次数(3次)、转向灯持续过长：次数(3次)
            'angle_threshold': '转角',         # 方向盘预警：转角(15度)、方向盘未回正：转角阈值(10度)
            'duration': '持续',               # 方向盘预警：持续(20秒)、转向灯持续过长：持续(20秒)
            'drive_time': '时间'              # 疲劳驾驶提醒：时间(2小时)
        }
        return labels.get(param, param)
    
    def get_param_unit(self, param: str) -> str:
        """获取参数单位"""
        units = {
            'delay_time': '秒',               # 延迟时间(3秒)
            'speed_threshold': 'km/h',        # 车速阈值(10km/h)、车速(60km/h)、车速(30km/h)
            'parking_time': '分钟',           # 停车时间(60分钟)
            'interval': '分钟',               # 间隔(60分钟) - 注意：转向灯的间隔是秒
            'max_count': '次',                # 次数(3次)
            'angle_threshold': '度',          # 转角(15度)、转角阈值(10度)
            'duration': '秒',                 # 持续(20秒)
            'drive_time': '小时'              # 时间(2小时)
        }
        return units.get(param, '')

    def get_param_unit_by_function(self, func_id: int, param: str) -> str:
        """根据功能ID和参数名称获取正确的单位"""
        # 特殊处理：转向灯持续过长(功能11)的间隔是秒，其他功能的间隔是分钟
        if param == 'interval':
            if func_id == 11:  # 转向灯持续过长
                return '秒'
            else:  # 停车未熄火提醒
                return '分钟'

        # 其他参数使用通用单位
        return self.get_param_unit(param)

    def bind_events(self):
        """绑定事件"""
        # 这里可以添加其他事件绑定
        pass

    def set_serial_communication(self, serial_comm: SerialCommunication):
        """设置串口通信对象"""
        self.serial_comm = serial_comm
        self.car_voice_manager.set_serial_communication(serial_comm)
        self.update_connection_status(True)

    def update_connection_status(self, connected: bool):
        """更新连接状态"""
        self.is_connected = connected
        if connected:
            self.status_label.config(text="●已连接", foreground="green")
            self.read_config_btn.config(state="normal")
            self.save_config_btn.config(state="normal")
            self.reset_config_btn.config(state="normal")
            self.test_all_btn.config(state="normal")
        else:
            self.status_label.config(text="●未连接", foreground="red")
            self.read_config_btn.config(state="disabled")
            self.save_config_btn.config(state="disabled")
            self.reset_config_btn.config(state="disabled")
            self.test_all_btn.config(state="disabled")

        # 更新功能测试按钮状态
        for widgets in self.function_widgets.values():
            widgets['test_btn'].config(state="normal" if connected else "disabled")

    def on_function_enabled_changed(self, func_id: int):
        """功能启用状态改变事件"""
        widgets = self.function_widgets[func_id]
        enabled = widgets['enabled_var'].get()

        # 显示/隐藏参数配置
        if enabled:
            widgets['params_frame'].pack(fill=tk.X, padx=10, pady=(0, 5))
        else:
            widgets['params_frame'].pack_forget()

        # 发送配置更新指令到ESP32
        self.send_function_config_update(func_id, enabled)

        # 标记配置已修改
        self.on_config_changed(func_id)

    def send_function_config_update(self, func_id: int, enabled: bool):
        """发送单个功能配置更新到ESP32"""
        try:
            if not self.is_connected or not self.serial_comm:
                self.logger.warning(f"设备未连接，无法发送功能{func_id}配置更新")
                return

            # 获取当前功能的完整配置
            widgets = self.function_widgets[func_id]
            config_data = {
                "function_id": func_id,
                "enabled": enabled
            }

            # 获取参数值
            param_widgets = widgets['param_widgets']
            for param_name, param_var in param_widgets.items():
                try:
                    config_data[param_name] = param_var.get()
                except Exception as e:
                    self.logger.warning(f"获取参数{param_name}值失败: {e}")
                    config_data[param_name] = 0

            # 发送SET_CAR_VOICE_CONFIG命令
            command = f"SET_CAR_VOICE_CONFIG {func_id}"
            self.logger.info(f"发送汽车语音功能{func_id}配置更新: {config_data}")

            response = self.serial_comm.send_command(command, config_data)

            if response and response.get("status") == "success":
                self.logger.info(f"功能{func_id}配置更新成功")
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.warning(f"功能{func_id}配置更新失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"发送功能{func_id}配置更新异常: {e}")

    def on_config_changed(self, func_id: int):
        """配置改变事件"""
        self.config_modified = True

        # 发送参数配置更新到ESP32
        if func_id in self.function_widgets:
            widgets = self.function_widgets[func_id]
            enabled = widgets['enabled_var'].get()
            self.send_function_config_update(func_id, enabled)

        # 可以在这里添加视觉提示，比如改变保存按钮颜色

    def update_all_status(self, config_data: dict):
        """更新所有功能状态到UI"""
        try:
            for func_id, config in config_data.items():
                if func_id in self.function_widgets:
                    widgets = self.function_widgets[func_id]

                    # 更新启用状态
                    if 'enabled' in config:
                        widgets['enabled_var'].set(config['enabled'])

                    # 更新参数值
                    param_widgets = widgets['param_widgets']
                    for param_name, param_value in config.items():
                        if param_name in param_widgets and param_name != 'enabled':
                            param_widgets[param_name].set(param_value)

            self.logger.info("已更新所有汽车语音功能的UI状态")

        except Exception as e:
            self.logger.error(f"更新汽车语音功能状态失败: {e}")

    def read_config(self):
        """读取配置"""
        if not self.is_connected:
            messagebox.showwarning("警告", "设备未连接")
            return

        try:
            success, config_data, message = self.car_voice_manager.get_config_from_device()

            if success:
                self.update_ui_from_config(config_data)
                self.config_modified = False
                messagebox.showinfo("成功", "配置读取成功")
            else:
                messagebox.showerror("错误", f"读取配置失败: {message}")

        except Exception as e:
            messagebox.showerror("错误", f"读取配置时发生错误: {str(e)}")

    def save_config(self):
        """保存配置"""
        if not self.is_connected:
            messagebox.showwarning("警告", "设备未连接")
            return

        try:
            # 从UI获取配置
            config_data = self.get_config_from_ui()

            # 发送到设备
            success, message = self.car_voice_manager.send_config_to_device(config_data)

            if success:
                self.config_modified = False
                messagebox.showinfo("成功", "配置保存成功")
            else:
                messagebox.showerror("错误", f"保存配置失败: {message}")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置时发生错误: {str(e)}")

    def reset_config(self):
        """重置配置"""
        if not self.is_connected:
            messagebox.showwarning("警告", "设备未连接")
            return

        if messagebox.askyesno("确认", "确定要重置所有配置为默认值吗？"):
            try:
                success, message = self.car_voice_manager.reset_config_to_default()

                if success:
                    # 更新UI显示
                    default_config = self.car_voice_manager.get_local_config()
                    self.update_ui_from_config(default_config)
                    self.config_modified = False
                    messagebox.showinfo("成功", "配置重置成功")
                else:
                    messagebox.showerror("错误", f"重置配置失败: {message}")

            except Exception as e:
                messagebox.showerror("错误", f"重置配置时发生错误: {str(e)}")

    def test_function(self, func_id: int):
        """测试单个功能"""
        if not self.is_connected:
            messagebox.showwarning("警告", "设备未连接")
            return

        try:
            success, message = self.car_voice_manager.test_voice_function(func_id)

            if success:
                messagebox.showinfo("测试", message)
            else:
                messagebox.showerror("测试失败", message)

        except Exception as e:
            messagebox.showerror("错误", f"测试功能时发生错误: {str(e)}")

    def test_all_functions(self):
        """测试所有功能"""
        if not self.is_connected:
            messagebox.showwarning("警告", "设备未连接")
            return

        try:
            success, results = self.car_voice_manager.test_all_functions()

            result_text = "\n".join(results)
            if success:
                messagebox.showinfo("测试完成", f"所有启用功能测试完成:\n\n{result_text}")
            else:
                messagebox.showwarning("测试完成", f"部分功能测试失败:\n\n{result_text}")

        except Exception as e:
            messagebox.showerror("错误", f"测试功能时发生错误: {str(e)}")

    def reset_function(self, func_id: int):
        """重置单个功能"""
        if messagebox.askyesno("确认", f"确定要重置功能{func_id}为默认配置吗？"):
            try:
                # 获取默认配置
                self.car_voice_manager.config.reset_to_default(func_id)
                default_config = self.car_voice_manager.config.get_config(func_id)

                # 更新UI
                self.update_function_ui(func_id, default_config)
                self.config_modified = True

            except Exception as e:
                messagebox.showerror("错误", f"重置功能时发生错误: {str(e)}")

    def export_config(self):
        """导出配置"""
        try:
            # 获取当前配置并更新到管理器
            config_data = self.get_config_from_ui()
            self.car_voice_manager.set_local_config(config_data)
            json_str = self.car_voice_manager.config.export_to_json()

            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="导出汽车语音配置",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(json_str)
                messagebox.showinfo("成功", f"配置已导出到: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出配置时发生错误: {str(e)}")

    def import_config(self):
        """导入配置"""
        try:
            # 选择文件
            filename = filedialog.askopenfilename(
                title="导入汽车语音配置",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'r', encoding='utf-8') as f:
                    json_str = f.read()

                # 导入配置
                self.car_voice_manager.import_config_from_json(json_str)
                config_data = self.car_voice_manager.get_local_config()

                # 更新UI
                self.update_ui_from_config(config_data)
                self.config_modified = True

                messagebox.showinfo("成功", f"配置已从 {filename} 导入")

        except Exception as e:
            messagebox.showerror("错误", f"导入配置时发生错误: {str(e)}")

    def get_config_from_ui(self) -> Dict[int, Dict[str, Any]]:
        """从UI获取配置数据"""
        config_data = {}

        for func_id, widgets in self.function_widgets.items():
            func_config = {
                'enabled': widgets['enabled_var'].get()
            }

            # 获取参数值
            for param, widget_var in widgets['param_widgets'].items():
                if param in ['start_time', 'end_time']:
                    func_config[param] = widget_var.get()
                else:
                    func_config[param] = widget_var.get()

            config_data[func_id] = func_config

        return config_data

    def update_ui_from_config(self, config_data: Dict[int, Dict[str, Any]]):
        """从配置数据更新UI"""
        for func_id, func_config in config_data.items():
            if func_id in self.function_widgets:
                self.update_function_ui(func_id, func_config)

    def update_function_ui(self, func_id: int, func_config: Dict[str, Any]):
        """更新单个功能的UI"""
        widgets = self.function_widgets[func_id]

        # 更新启用状态
        enabled = func_config.get('enabled', False)
        widgets['enabled_var'].set(enabled)

        # 显示/隐藏参数配置
        if enabled:
            widgets['params_frame'].pack(fill=tk.X, padx=10, pady=(0, 5))
        else:
            widgets['params_frame'].pack_forget()

        # 更新参数值
        for param, widget_var in widgets['param_widgets'].items():
            if param in func_config:
                widget_var.set(func_config[param])

    def on_config_changed(self, func_id: int):
        """配置改变事件"""
        # 使用func_id参数（避免IDE警告）
        _ = func_id
        self.config_modified = True
        # 可以在这里添加视觉提示，比如改变保存按钮颜色
