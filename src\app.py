#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
小智ESP32指令管理系统 - 应用程序主类
作者: AI Assistant
版本: 2.0.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging

# 添加当前目录到路径，确保能找到模块
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from core.log_manager import LogManager
from gui.main_window import MainWindow


class XiaoZhiApp:
    """小智ESP32指令管理系统应用程序主类"""
    
    def __init__(self):
        """初始化应用程序"""
        self.logger = None
        self.main_window = None
        
    def run(self):
        """运行应用程序"""
        try:
            # 初始化日志管理器
            log_manager = LogManager()
            self.logger = logging.getLogger(__name__)
            self.logger.info("小智ESP32指令管理系统启动 v2.0")
            
            # 创建主窗口
            self.main_window = MainWindow()
            
            # 运行应用程序
            self.main_window.run()
            
        except Exception as e:
            error_msg = f"程序启动失败: {str(e)}"
            print(error_msg)
            
            # 尝试显示错误对话框
            try:
                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口
                messagebox.showerror("启动错误", error_msg)
                root.destroy()
            except:
                pass
            
            sys.exit(1)
