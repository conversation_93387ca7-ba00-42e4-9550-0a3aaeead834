@echo off
chcp 65001 > nul
title <PERSON><PERSON>hi ESP32 Command Manager - Build Tool
echo ========================================
echo XiaoZhi ESP32 Command Manager - Build Tool v1.0
echo ========================================
echo.
echo Starting build process...
echo Current directory: %CD%
echo.

:: Set Python path - auto detect multiple possible Python paths
set "PYTHON_PATH="
for %%p in (
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"
    "C:\Python310\python.exe"
    "C:\Python311\python.exe"
    "C:\Python312\python.exe"
    "python.exe"
) do (
    if exist %%p (
        set "PYTHON_PATH=%%~p"
        goto :found_python
    )
)

:found_python
if "%PYTHON_PATH%"=="" (
    echo Error: Python installation not found
    echo Please ensure Python is correctly installed and in PATH
    echo Supported Python versions: 3.9, 3.10, 3.11, 3.12
    pause
    exit /b 1
)

echo Found Python: %PYTHON_PATH%
echo.

:: Check if main program file exists
if not exist "main.py" (
    echo Error: main.py file not found
    echo Please ensure you are running this script in the correct directory
    pause
    exit /b 1
)

:: Check necessary directory structure
if not exist "gui" (
    echo Error: gui directory not found
    echo Please ensure project structure is complete
    pause
    exit /b 1
)

if not exist "core" (
    echo Error: core directory not found
    echo Please ensure project structure is complete
    pause
    exit /b 1
)

echo Installing/updating required packages...
"%PYTHON_PATH%" -m pip install --upgrade pip
"%PYTHON_PATH%" -m pip install pyinstaller
"%PYTHON_PATH%" -m pip install pyserial
"%PYTHON_PATH%" -m pip install -r requirements.txt

echo.
echo Building executable file...
echo This may take a few minutes, please wait...
echo.

:: Clean previous build files
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"

:: Use PyInstaller to build
"%PYTHON_PATH%" -m PyInstaller --clean --noconfirm --onefile --windowed --name "XiaoZhiESP32Manager" --hidden-import tkinter --hidden-import tkinter.ttk --hidden-import tkinter.messagebox --hidden-import tkinter.filedialog --hidden-import tkinter.simpledialog --hidden-import serial --hidden-import serial.tools.list_ports --hidden-import logging --hidden-import threading --hidden-import json --hidden-import configparser --hidden-import datetime --hidden-import time --hidden-import re --hidden-import os --hidden-import sys --collect-all serial --collect-all serial.tools main.py

echo.
if exist "dist\XiaoZhiESP32Manager.exe" (
    echo ========================================
    echo Build successful!
    echo ========================================
    echo.
    echo Executable file location: dist\XiaoZhiESP32Manager.exe
    echo File size: 
    for %%f in ("dist\XiaoZhiESP32Manager.exe") do echo   %%~zf bytes
    echo.
    echo Usage instructions:
    echo 1. The executable file is standalone, no need to install Python
    echo 2. You can copy the exe file to any Windows computer
    echo 3. First run may be blocked by antivirus, please add to trust list
    echo 4. Ensure target computer has USB-to-serial drivers
    echo.
    echo Package contents:
    echo   - Main program (main.py)
    echo   - GUI interface modules (gui/*)
    echo   - Core function modules (core/*)
    echo   - Utility modules (utils/*)
    echo   - Configuration file templates
    echo   - Log directory
    echo.
    echo You can now distribute this exe file!
) else (
    echo ========================================
    echo Build failed
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Common issues:
    echo 1. Incompatible Python version (need 3.9+)
    echo 2. Missing required packages
    echo 3. Insufficient disk space
    echo 4. Insufficient permissions (try running as administrator)
    echo.
)

echo.
echo Cleaning temporary files...
if exist "build" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"

echo.
echo Press any key to exit...
pause > nul
