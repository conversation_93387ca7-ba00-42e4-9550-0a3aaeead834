#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理模块
负责应用程序日志的配置和管理
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import List, Optional
import threading

class LogManager:
    """日志管理类"""
    
    def __init__(self, log_file: str = "logs/app.log", level: str = "INFO"):
        self.log_file = log_file
        self.level = level
        self.log_records: List[str] = []
        self.max_records = 1000  # 内存中保存的最大日志条数
        self.lock = threading.Lock()
        
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志配置"""
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(self.log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 设置日志级别
            numeric_level = getattr(logging, self.level.upper(), logging.INFO)
            
            # 创建根日志记录器
            root_logger = logging.getLogger()
            root_logger.setLevel(numeric_level)
            
            # 清除现有的处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 创建文件处理器（带轮转）
            file_handler = logging.handlers.RotatingFileHandler(
                self.log_file,
                maxBytes=10*1024*1024,  # 10MB
                backupCount=5,
                encoding='utf-8'
            )
            file_handler.setLevel(numeric_level)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(numeric_level)
            
            # 创建内存处理器（用于GUI显示）
            memory_handler = MemoryHandler(self)
            memory_handler.setLevel(numeric_level)
            
            # 设置日志格式
            formatter = logging.Formatter(
                '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            memory_handler.setFormatter(formatter)
            
            # 添加处理器到根日志记录器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(console_handler)
            root_logger.addHandler(memory_handler)
            
            # 记录初始化信息
            logger = logging.getLogger(__name__)
            logger.info(f"日志系统初始化完成，级别: {self.level}, 文件: {self.log_file}")
            
        except Exception as e:
            print(f"日志系统初始化失败: {e}")
    
    def add_log_record(self, record: str):
        """添加日志记录到内存"""
        with self.lock:
            self.log_records.append(record)
            
            # 保持记录数量在限制内
            if len(self.log_records) > self.max_records:
                self.log_records = self.log_records[-self.max_records:]
    
    def get_log_records(self, count: Optional[int] = None) -> List[str]:
        """
        获取日志记录
        
        Args:
            count: 获取的记录数量，None表示获取所有
            
        Returns:
            List[str]: 日志记录列表
        """
        with self.lock:
            if count is None:
                return self.log_records.copy()
            else:
                return self.log_records[-count:] if count > 0 else []
    
    def clear_log_records(self):
        """清空内存中的日志记录"""
        with self.lock:
            self.log_records.clear()
    
    def search_logs(self, keyword: str, case_sensitive: bool = False) -> List[str]:
        """
        搜索日志记录
        
        Args:
            keyword: 搜索关键词
            case_sensitive: 是否区分大小写
            
        Returns:
            List[str]: 匹配的日志记录
        """
        with self.lock:
            if not case_sensitive:
                keyword = keyword.lower()
                return [record for record in self.log_records 
                       if keyword in record.lower()]
            else:
                return [record for record in self.log_records 
                       if keyword in record]
    
    def get_log_file_content(self, lines: int = 100) -> str:
        """
        读取日志文件内容
        
        Args:
            lines: 读取的行数
            
        Returns:
            str: 日志文件内容
        """
        try:
            if not os.path.exists(self.log_file):
                return "日志文件不存在"
            
            with open(self.log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
                
            # 返回最后N行
            if lines > 0:
                return ''.join(all_lines[-lines:])
            else:
                return ''.join(all_lines)
                
        except Exception as e:
            return f"读取日志文件失败: {e}"
    
    def set_log_level(self, level: str):
        """
        设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        try:
            numeric_level = getattr(logging, level.upper(), logging.INFO)
            
            # 更新所有处理器的级别
            root_logger = logging.getLogger()
            root_logger.setLevel(numeric_level)
            
            for handler in root_logger.handlers:
                handler.setLevel(numeric_level)
            
            self.level = level.upper()
            
            logger = logging.getLogger(__name__)
            logger.info(f"日志级别已更改为: {self.level}")
            
        except Exception as e:
            logger = logging.getLogger(__name__)
            logger.error(f"设置日志级别失败: {e}")

class MemoryHandler(logging.Handler):
    """内存日志处理器，用于GUI显示"""
    
    def __init__(self, log_manager: LogManager):
        super().__init__()
        self.log_manager = log_manager
    
    def emit(self, record):
        """发送日志记录"""
        try:
            msg = self.format(record)
            self.log_manager.add_log_record(msg)
        except Exception:
            self.handleError(record)

class GUILogHandler:
    """GUI日志处理器，用于在界面中显示日志"""
    
    def __init__(self, log_manager: LogManager):
        self.log_manager = log_manager
        self.callbacks = []
    
    def add_callback(self, callback):
        """添加日志更新回调函数"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)
    
    def remove_callback(self, callback):
        """移除日志更新回调函数"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def get_recent_logs(self, count: int = 50) -> List[str]:
        """获取最近的日志记录"""
        return self.log_manager.get_log_records(count)
    
    def notify_callbacks(self):
        """通知所有回调函数"""
        for callback in self.callbacks:
            try:
                callback()
            except Exception as e:
                # 避免回调函数错误影响日志系统
                pass
