@echo off
chcp 65001 > nul
title 快速打包 - 小智ESP32指令管理器
echo 🚀 快速打包 - 小智ESP32指令管理器
echo ================================
echo.

:: 检查主程序文件
if not exist "main.py" (
    echo ❌ 错误: main.py 文件未找到
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python
    echo 请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

echo 📦 正在打包，请稍候...
echo.

:: 清理旧文件
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "build" rmdir /s /q "build" 2>nul

:: 安装必要的包
echo 安装依赖包...
python -m pip install pyinstaller pyserial --quiet

:: 使用spec文件打包（如果存在）
if exist "xiaozhi_manager.spec" (
    echo 使用spec文件打包...
    python -m PyInstaller xiaozhi_manager.spec --clean --noconfirm
) else (
    echo 使用默认配置打包...
    python -m PyInstaller --onefile --windowed --name "小智ESP32指令管理器" main.py
)

echo.
if exist "dist\小智ESP32指令管理器.exe" (
    echo ✅ 打包成功！
    echo 📁 文件位置: dist\小智ESP32指令管理器.exe
    
    :: 复制必要文件
    if exist "config.ini" copy "config.ini" "dist\" >nul 2>&1
    if not exist "dist\logs" mkdir "dist\logs"
    
    echo.
    echo 🎉 可以开始使用了！
    echo.
    echo 要立即运行测试吗？(Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start "" "dist\小智ESP32指令管理器.exe"
    )
) else (
    echo ❌ 打包失败
    echo 请检查错误信息或使用 build_xiaozhi_new.bat 进行详细打包
)

echo.
pause
