# -*- mode: python ; coding: utf-8 -*-
"""
小智ESP32指令管理器 PyInstaller 配置文件
"""

import os
import sys

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 添加项目路径到Python路径
sys.path.insert(0, project_root)

# 定义需要包含的数据文件
datas = []

# 添加配置文件
config_file = os.path.join(project_root, 'config.ini')
if os.path.exists(config_file):
    datas.append((config_file, '.'))

# 添加资源目录（如果存在）
resources_dir = os.path.join(project_root, 'resources')
if os.path.exists(resources_dir):
    for root, dirs, files in os.walk(resources_dir):
        for file in files:
            file_path = os.path.join(root, file)
            rel_path = os.path.relpath(file_path, project_root)
            dest_dir = os.path.dirname(rel_path)
            datas.append((file_path, dest_dir))

# 定义隐藏导入
hiddenimports = [
    # 标准库
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'tkinter.simpledialog',
    'logging',
    'threading',
    'json',
    'configparser',
    'datetime',
    'time',
    'os',
    'sys',
    're',
    'queue',
    'typing',
    
    # 第三方库
    'serial',
    'serial.tools',
    'serial.tools.list_ports',
    
    # 项目模块
    'core',
    'core.serial_comm',
    'core.command_manager',
    'core.config_manager',
    'core.log_manager',
    'core.uart_info_manager',
    'gui',
    'gui.main_window',
    'gui.command_dialog',
    'gui.settings_dialog',
    'utils',
    'utils.validators',
    'utils.json_utils',
]

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'wx',
    ],
    noarchive=False,
    optimize=0,
)

# 创建PYZ文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建可执行文件
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='小智ESP32指令管理器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 窗口模式，不显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以在这里添加图标文件路径
)
