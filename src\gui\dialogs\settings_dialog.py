#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置对话框模块
实现应用程序设置界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from typing import Optional

from core.config_manager import ConfigManager
from utils.validators import Validators

class SettingsDialog:
    """设置对话框类"""
    
    def __init__(self, parent, config_manager: ConfigManager):
        self.parent = parent
        self.config_manager = config_manager
        self.result = False
        self.logger = logging.getLogger(__name__)
        
        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("设置")
        self.dialog.geometry("500x600")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)
        
        # 居中显示
        self.center_window()
        
        # 变量
        self.setup_variables()
        
        # 创建界面
        self.setup_ui()
        
        # 加载当前设置
        self.load_settings()
        
        # 绑定事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel_clicked)
    
    def center_window(self):
        """居中显示窗口"""
        try:
            self.dialog.update_idletasks()
            
            # 获取窗口尺寸
            width = self.dialog.winfo_width()
            height = self.dialog.winfo_height()
            
            # 获取父窗口位置和尺寸
            parent_x = self.parent.winfo_x()
            parent_y = self.parent.winfo_y()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # 计算居中位置
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
            
            self.dialog.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            self.logger.error(f"窗口居中失败: {e}")
    
    def setup_variables(self):
        """设置变量"""
        # 串口设置
        self.baudrate_var = tk.StringVar()
        self.auto_connect_var = tk.BooleanVar()
        
        # 界面设置
        self.font_size_var = tk.StringVar()
        self.auto_refresh_var = tk.BooleanVar()
        self.refresh_interval_var = tk.StringVar()
        
        # 日志设置
        self.log_level_var = tk.StringVar()
        self.log_file_var = tk.StringVar()
        self.max_log_size_var = tk.StringVar()
        self.backup_count_var = tk.StringVar()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # 串口设置标签页
        self.create_serial_tab(notebook)
        
        # 界面设置标签页
        self.create_ui_tab(notebook)
        
        # 日志设置标签页
        self.create_log_tab(notebook)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="应用", command=self.apply_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="确定", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="恢复默认", command=self.reset_defaults).pack(side=tk.LEFT)
    
    def create_serial_tab(self, notebook):
        """创建串口设置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="串口设置")
        
        # 波特率设置
        baudrate_frame = ttk.LabelFrame(frame, text="波特率")
        baudrate_frame.pack(fill=tk.X, padx=10, pady=10)
        
        baudrate_inner = ttk.Frame(baudrate_frame)
        baudrate_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(baudrate_inner, text="波特率:").pack(side=tk.LEFT)
        baudrate_combo = ttk.Combobox(baudrate_inner, textvariable=self.baudrate_var, 
                                     values=["9600", "14400", "19200", "38400", "57600", "115200"],
                                     state="readonly", width=10)
        baudrate_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Label(baudrate_inner, text="(推荐: 19200)", foreground="gray").pack(side=tk.LEFT, padx=(5, 0))
        
        # 连接设置
        connect_frame = ttk.LabelFrame(frame, text="连接设置")
        connect_frame.pack(fill=tk.X, padx=10, pady=10)
        
        connect_inner = ttk.Frame(connect_frame)
        connect_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(connect_inner, text="启动时自动连接到上次使用的端口", 
                       variable=self.auto_connect_var).pack(anchor=tk.W)
        
        # 说明
        info_frame = ttk.LabelFrame(frame, text="说明")
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        info_text = """串口连接参数说明：
• 波特率：与ESP32设备通信的速率，默认19200（指令模式）
• 数据位：8位（固定）
• 停止位：1位（固定）
• 奇偶校验：无（固定）
• 流控：无（固定）

连接方式：
USB TTL模块 → ESP32 UART2
TX → GPIO9 (ESP32 RX)
RX → GPIO8 (ESP32 TX)
GND → GND"""
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT, font=("", 8))
        info_label.pack(padx=10, pady=10, anchor=tk.W)
    
    def create_ui_tab(self, notebook):
        """创建界面设置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="界面设置")
        
        # 字体设置
        font_frame = ttk.LabelFrame(frame, text="字体设置")
        font_frame.pack(fill=tk.X, padx=10, pady=10)
        
        font_inner = ttk.Frame(font_frame)
        font_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(font_inner, text="字体大小:").pack(side=tk.LEFT)
        font_combo = ttk.Combobox(font_inner, textvariable=self.font_size_var,
                                 values=["8", "9", "10", "11", "12", "14", "16"],
                                 state="readonly", width=5)
        font_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # 刷新设置
        refresh_frame = ttk.LabelFrame(frame, text="自动刷新")
        refresh_frame.pack(fill=tk.X, padx=10, pady=10)
        
        refresh_inner = ttk.Frame(refresh_frame)
        refresh_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Checkbutton(refresh_inner, text="启用自动刷新", 
                       variable=self.auto_refresh_var).pack(anchor=tk.W)
        
        interval_frame = ttk.Frame(refresh_inner)
        interval_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(interval_frame, text="刷新间隔:").pack(side=tk.LEFT)
        interval_combo = ttk.Combobox(interval_frame, textvariable=self.refresh_interval_var,
                                     values=["1", "2", "3", "5", "10", "15", "30"],
                                     state="readonly", width=5)
        interval_combo.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(interval_frame, text="秒").pack(side=tk.LEFT, padx=(2, 0))
    
    def create_log_tab(self, notebook):
        """创建日志设置标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="日志设置")
        
        # 日志级别
        level_frame = ttk.LabelFrame(frame, text="日志级别")
        level_frame.pack(fill=tk.X, padx=10, pady=10)
        
        level_inner = ttk.Frame(level_frame)
        level_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(level_inner, text="级别:").pack(side=tk.LEFT)
        level_combo = ttk.Combobox(level_inner, textvariable=self.log_level_var,
                                  values=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                                  state="readonly", width=10)
        level_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # 日志文件
        file_frame = ttk.LabelFrame(frame, text="日志文件")
        file_frame.pack(fill=tk.X, padx=10, pady=10)
        
        file_inner = ttk.Frame(file_frame)
        file_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(file_inner, text="文件路径:").pack(anchor=tk.W)
        
        path_frame = ttk.Frame(file_inner)
        path_frame.pack(fill=tk.X, pady=(5, 0))
        
        self.log_file_entry = ttk.Entry(path_frame, textvariable=self.log_file_var)
        self.log_file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(path_frame, text="浏览", command=self.browse_log_file).pack(side=tk.RIGHT, padx=(5, 0))
        
        # 日志轮转
        rotation_frame = ttk.LabelFrame(frame, text="日志轮转")
        rotation_frame.pack(fill=tk.X, padx=10, pady=10)
        
        rotation_inner = ttk.Frame(rotation_frame)
        rotation_inner.pack(fill=tk.X, padx=10, pady=10)
        
        size_frame = ttk.Frame(rotation_inner)
        size_frame.pack(fill=tk.X)
        
        ttk.Label(size_frame, text="最大文件大小:").pack(side=tk.LEFT)
        size_combo = ttk.Combobox(size_frame, textvariable=self.max_log_size_var,
                                 values=["1", "5", "10", "20", "50"],
                                 state="readonly", width=5)
        size_combo.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(size_frame, text="MB").pack(side=tk.LEFT, padx=(2, 0))
        
        count_frame = ttk.Frame(rotation_inner)
        count_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(count_frame, text="备份文件数量:").pack(side=tk.LEFT)
        count_combo = ttk.Combobox(count_frame, textvariable=self.backup_count_var,
                                  values=["1", "3", "5", "10", "20"],
                                  state="readonly", width=5)
        count_combo.pack(side=tk.LEFT, padx=(5, 0))
    
    def browse_log_file(self):
        """浏览日志文件"""
        try:
            filename = filedialog.asksaveasfilename(
                title="选择日志文件位置",
                defaultextension=".log",
                filetypes=[("日志文件", "*.log"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if filename:
                self.log_file_var.set(filename)
                
        except Exception as e:
            self.logger.error(f"浏览日志文件失败: {e}")
    
    def load_settings(self):
        """加载当前设置"""
        try:
            # 串口设置
            self.baudrate_var.set(str(self.config_manager.get_serial_baudrate()))
            self.auto_connect_var.set(self.config_manager.get_auto_connect())
            
            # 界面设置
            self.font_size_var.set(self.config_manager.get('ui', 'font_size', '9'))
            self.auto_refresh_var.set(self.config_manager.get_bool('ui', 'auto_refresh', True))
            self.refresh_interval_var.set(self.config_manager.get('ui', 'refresh_interval', '5'))
            
            # 日志设置
            self.log_level_var.set(self.config_manager.get_log_level())
            self.log_file_var.set(self.config_manager.get_log_file_path())
            
            max_size_mb = self.config_manager.get_int('logging', 'max_size', 10485760) // (1024 * 1024)
            self.max_log_size_var.set(str(max_size_mb))
            
            self.backup_count_var.set(self.config_manager.get('logging', 'backup_count', '5'))
            
        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 串口设置
            self.config_manager.set_serial_baudrate(int(self.baudrate_var.get()))
            self.config_manager.set_auto_connect(self.auto_connect_var.get())
            
            # 界面设置
            self.config_manager.set('ui', 'font_size', self.font_size_var.get())
            self.config_manager.set('ui', 'auto_refresh', self.auto_refresh_var.get())
            self.config_manager.set('ui', 'refresh_interval', self.refresh_interval_var.get())
            
            # 日志设置
            self.config_manager.set('logging', 'level', self.log_level_var.get())
            self.config_manager.set('logging', 'file_path', self.log_file_var.get())
            
            max_size_bytes = int(self.max_log_size_var.get()) * 1024 * 1024
            self.config_manager.set('logging', 'max_size', max_size_bytes)
            self.config_manager.set('logging', 'backup_count', self.backup_count_var.get())
            
            # 保存配置文件
            self.config_manager.save_config()
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存设置失败: {e}")
            messagebox.showerror("错误", f"保存设置失败: {e}")
            return False
    
    def reset_defaults(self):
        """恢复默认设置"""
        try:
            if messagebox.askyesno("确认", "确定要恢复所有设置为默认值吗？"):
                # 恢复默认值
                self.baudrate_var.set("19200")
                self.auto_connect_var.set(False)
                
                self.font_size_var.set("9")
                self.auto_refresh_var.set(True)
                self.refresh_interval_var.set("5")
                
                self.log_level_var.set("INFO")
                self.log_file_var.set("logs/app.log")
                self.max_log_size_var.set("10")
                self.backup_count_var.set("5")
                
        except Exception as e:
            self.logger.error(f"恢复默认设置失败: {e}")
    
    def ok_clicked(self):
        """确定按钮点击事件"""
        if self.save_settings():
            self.result = True
            self.dialog.destroy()
    
    def apply_clicked(self):
        """应用按钮点击事件"""
        if self.save_settings():
            messagebox.showinfo("成功", "设置已保存")
    
    def cancel_clicked(self):
        """取消按钮点击事件"""
        self.result = False
        self.dialog.destroy()
