#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证模块
提供各种数据验证功能
"""

import re
import logging
from typing import Any, List, Optional

class Validators:
    """数据验证类"""
    
    @staticmethod
    def validate_command_name(name: str) -> bool:
        """
        验证指令名称
        
        Args:
            name: 指令名称
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(name, str):
            return False
        
        # 不能为空
        if not name.strip():
            return False
        
        # 长度限制
        if len(name) > 50:
            return False
        
        # 只允许字母、数字、下划线、中文
        pattern = r'^[a-zA-Z0-9_\u4e00-\u9fff]+$'
        return bool(re.match(pattern, name))
    
    @staticmethod
    def validate_command_text(text: str) -> bool:
        """
        验证指令文本
        
        Args:
            text: 指令文本
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(text, str):
            return False
        
        # 不能为空
        if not text.strip():
            return False
        
        # 长度限制
        if len(text) > 500:
            return False
        
        return True
    
    @staticmethod
    def validate_command_description(description: str) -> bool:
        """
        验证指令描述
        
        Args:
            description: 指令描述
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(description, str):
            return False
        
        # 描述可以为空
        if not description.strip():
            return True
        
        # 长度限制
        if len(description) > 200:
            return False
        
        return True
    
    @staticmethod
    def validate_serial_port(port: str) -> bool:
        """
        验证串口名称
        
        Args:
            port: 串口名称
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(port, str):
            return False
        
        if not port.strip():
            return False
        
        # Windows COM端口格式
        if re.match(r'^COM\d+$', port.upper()):
            return True
        
        # Linux/Mac 串口格式
        if re.match(r'^/dev/(tty|cu)\w+$', port):
            return True
        
        return False
    
    @staticmethod
    def validate_baudrate(baudrate: int) -> bool:
        """
        验证波特率
        
        Args:
            baudrate: 波特率
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(baudrate, int):
            return False
        
        # 常见波特率
        valid_baudrates = [
            9600, 14400, 19200, 38400, 57600, 115200, 
            128000, 256000, 460800, 921600
        ]
        
        return baudrate in valid_baudrates
    
    @staticmethod
    def validate_file_path(file_path: str) -> bool:
        """
        验证文件路径
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(file_path, str):
            return False
        
        if not file_path.strip():
            return False
        
        # 检查路径长度
        if len(file_path) > 260:  # Windows路径长度限制
            return False
        
        # 检查非法字符
        illegal_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in illegal_chars:
            if char in file_path:
                return False
        
        return True
    
    @staticmethod
    def validate_json_backup(data: Any) -> bool:
        """
        验证备份JSON数据格式
        
        Args:
            data: 备份数据
            
        Returns:
            bool: 是否有效
        """
        if not isinstance(data, dict):
            return False
        
        # 必须包含custom_commands字段
        if 'custom_commands' not in data:
            return False
        
        custom_commands = data['custom_commands']
        if not isinstance(custom_commands, dict):
            return False
        
        # 验证每个指令的格式
        for name, info in custom_commands.items():
            if not isinstance(name, str) or not isinstance(info, dict):
                return False
            
            # 必须包含text字段
            if 'text' not in info:
                return False
            
            if not isinstance(info['text'], str):
                return False
        
        return True
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 1000) -> str:
        """
        清理用户输入
        
        Args:
            text: 输入文本
            max_length: 最大长度
            
        Returns:
            str: 清理后的文本
        """
        if not isinstance(text, str):
            return ""
        
        # 去除首尾空白
        text = text.strip()
        
        # 限制长度
        if len(text) > max_length:
            text = text[:max_length]
        
        # 移除控制字符（保留换行和制表符）
        text = ''.join(char for char in text 
                      if ord(char) >= 32 or char in '\n\t')
        
        return text
    
    @staticmethod
    def validate_window_geometry(width: int, height: int, x: int, y: int) -> bool:
        """
        验证窗口几何参数
        
        Args:
            width: 窗口宽度
            height: 窗口高度
            x: 窗口X坐标
            y: 窗口Y坐标
            
        Returns:
            bool: 是否有效
        """
        # 检查类型
        if not all(isinstance(val, int) for val in [width, height, x, y]):
            return False
        
        # 检查最小尺寸
        if width < 400 or height < 300:
            return False
        
        # 检查最大尺寸（防止超出屏幕）
        if width > 3840 or height > 2160:  # 4K分辨率
            return False
        
        # 检查坐标范围
        if x < -1000 or y < -1000 or x > 3840 or y > 2160:
            return False
        
        return True
    
    @staticmethod
    def get_validation_errors(name: str, text: str, description: str) -> List[str]:
        """
        获取指令验证错误列表
        
        Args:
            name: 指令名称
            text: 指令文本
            description: 指令描述
            
        Returns:
            List[str]: 错误信息列表
        """
        errors = []
        
        if not Validators.validate_command_name(name):
            errors.append("指令名称无效：只能包含字母、数字、下划线和中文，长度不超过50字符")
        
        if not Validators.validate_command_text(text):
            errors.append("指令文本无效：不能为空，长度不超过500字符")
        
        if not Validators.validate_command_description(description):
            errors.append("指令描述无效：长度不超过200字符")
        
        return errors
