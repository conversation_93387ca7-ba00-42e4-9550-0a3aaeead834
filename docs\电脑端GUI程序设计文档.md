# 小智ESP32指令管理工具 - 电脑端GUI程序设计文档

## 1. 项目概述

### 1.1 项目目标
开发一个Windows桌面应用程序，通过串口与ESP32设备通信，实现对小智设备的文本指令管理和配置。

### 1.2 主要功能
- 串口连接管理
- 系统指令查询（只读）
- 文本指令管理（增删改查）
- 指令执行测试
- 配置备份与恢复
- 实时日志显示

### 1.3 技术栈
- **开发语言**: Python (推荐) / C# / Electron
- **UI框架**: tkinter / PyQt5/6 / WPF
- **串口通信**: pyserial库
- **数据格式**: 简化文本协议 (CMD:PARAM1:PARAM2\n)

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    串口通信    ┌─────────────────┐
│   电脑端GUI     │ ←----------→ │   ESP32设备     │
│                 │   文本协议    │                 │
│ - 连接管理      │              │ - 指令处理      │
│ - 指令管理      │              │ - 状态反馈      │
│ - 配置管理      │              │ - NVS存储       │
│ - 日志显示      │              │ - 小智集成      │
└─────────────────┘              └─────────────────┘
```

### 2.2 通信协议
- **传输方式**: UART串口
- **数据格式**: 简化文本协议
- **编码方式**: UTF-8
- **结束符**: `\n`
- **超时时间**: 5秒

## 3. 硬件连接

### 3.1 串口配置
- **ESP32端口**: UART2
- **GPIO配置**:
  - 指令模式: TX=GPIO17, RX=GPIO19
  - 汽车模式: TX=GPIO8, RX=GPIO9
- **波特率**:
  - 指令模式: 19200
  - 汽车模式: 19200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

### 3.2 连接方式
1. **有线连接**: USB转TTL模块连接ESP32的GPIO17/19（指令模式）
2. **无线连接**: 通过蓝牙模块(如HC-05)实现无线通信（未来扩展）

### 3.3 切换到指令模式
在ESP32网络控制台中发送 `cmd_gpio` 指令，将UART2切换到指令管理模式。
- 默认启动为汽车语音模式 (GPIO8/9, 19200)
- 需要手动切换到指令模式 (GPIO17/19, 115200)

## 4. 通信协议详细设计

### 4.1 基础协议格式
```
命令格式：CMD:参数1:参数2\n
响应格式：状态:数据\n
```

**协议特点**：
- **简化文本协议**：使用冒号分隔的文本格式，易于解析
- **UTF-8编码**：支持中文字符
- **换行符结束**：每条命令/响应以`\n`结束
- **字符转义**：特殊字符使用转义序列

### 4.2 字符转义规则
| 原字符 | 转义序列 | 说明 |
|--------|----------|------|
| `|` | `\p` | 管道符转义 |
| `\` | `\\` | 反斜杠转义 |
| `\n` | `\n` | 换行符转义 |
| `\r` | `\r` | 回车符转义 |

## 5. 支持的命令详细说明

### 5.1 连接测试命令

#### 5.1.1 PING测试
**请求：**
```
PING
```

**响应：**
```
PONG
```

### 5.2 查询类命令

#### 5.2.1 获取系统指令列表
**请求：**
```
LIST_SYS
```

**响应：**
```
OK:help,wifi,4g,reboot,ask,now,time,weather,car_gpio,cmd_gpio,gpio_status,ota,settings
```

#### 5.2.2 获取文本指令列表
**请求：**
```
LIST_TEXT
```

**响应：**
```
OK:music=播放音乐,weather=今天天气怎么样
```

#### 5.2.3 获取系统指令详情
**请求：**
```
GET_SYS:ask
```

**响应：**
```
OK:ask=发送文本给小智AI并获取回复
```

#### 5.2.4 获取文本指令详情
**请求：**
```
GET_TEXT:music
```

**响应：**
```
OK:music=播放音乐
```

#### 5.2.5 获取系统状态
**请求：**
```
STATUS
```

**响应：**
```
OK:connected,sys_cmds:13,text_cmds:2,storage:ok,free_mem:95183
```

### 5.3 管理类命令

#### 5.3.1 添加文本指令
**请求：**
```
ADD:music:播放音乐
```

**响应：**
```
OK:added
```

**错误响应：**
```
ERROR:command exists        # 指令已存在
ERROR:invalid parameters    # 参数无效
ERROR:storage full         # 存储空间已满
ERROR:storage failed       # 存储操作失败
```

#### 5.3.2 修改文本指令
**请求：**
```
MOD:music:播放我喜欢的音乐
```

**响应：**
```
OK:modified
```

**错误响应：**
```
ERROR:command not found     # 指令不存在
ERROR:invalid parameters    # 参数无效
ERROR:storage failed       # 存储操作失败
```

#### 5.3.3 删除文本指令
**请求：**
```
DEL:music
```

**响应：**
```
OK:deleted
```

**错误响应：**
```
ERROR:command not found     # 指令不存在
ERROR:storage failed       # 存储操作失败
```

#### 5.3.4 执行指令
**请求：**
```
EXEC:music
```

**响应：**
```
OK:executed                 # 执行成功
```

**错误响应：**
```
ERROR:command not found     # 指令不存在
ERROR:execution failed      # 执行失败
ERROR:system command protected  # 系统指令受保护
```

### 5.4 批量操作命令

#### 5.4.1 备份所有文本指令
**请求：**
```
BACKUP
```

**响应：**
```
OK:music=播放音乐,weather=今天天气怎么样
```

#### 5.4.2 恢复文本指令
**请求：**
```
RESTORE:music=播放音乐,weather=今天天气怎么样
```

**响应：**
```
OK:restored
```

**错误响应：**
```
ERROR:invalid restore data  # 恢复数据格式错误
ERROR:restore failed       # 恢复操作失败
```

#### 5.4.3 重置所有文本指令
**请求：**
```
RESET
```

**响应：**
```
OK:reset
```

**错误响应：**
```
ERROR:reset failed         # 重置操作失败
```

### 5.5 错误处理

#### 5.5.1 通用错误响应格式
```
ERROR:错误信息
```

#### 5.5.2 常见错误类型
| 错误信息 | 说明 | 可能原因 |
|----------|------|----------|
| `command not found` | 指令不存在 | 指令名称错误或未定义 |
| `invalid parameters` | 参数无效 | 参数格式错误或缺失 |
| `command exists` | 指令已存在 | 尝试添加重复指令 |
| `storage full` | 存储空间已满 | 已达到最大指令数量(50个) |
| `storage failed` | 存储操作失败 | NVS存储系统错误 |
| `execution failed` | 指令执行失败 | 文本交互系统错误 |
| `system command protected` | 系统指令受保护 | 尝试执行系统指令 |
| `invalid restore data` | 恢复数据无效 | 备份数据格式错误 |
| `reset failed` | 重置失败 | 清空操作失败 |

### 5.6 系统指令列表

#### 5.6.1 支持的系统指令
| 指令名称 | 功能描述 | 分类 |
|----------|----------|------|
| `help` | 显示所有可用命令的帮助信息 | 帮助 |
| `wifi` | WiFi网络连接和管理 | 网络 |
| `4g` | 4G网络连接和管理 | 网络 |
| `reboot` | 重启设备 | 系统 |
| `ask` | 发送文本给小智AI并获取回复 | 交互 |
| `now` | 播报当前完整信息 | 信息 |
| `time` | 查询当前时间 | 信息 |
| `weather` | 查询天气信息 | 信息 |
| `car_gpio` | 切换UART2到汽车语音模式 | 配置 |
| `cmd_gpio` | 切换UART2到指令管理模式 | 配置 |
| `gpio_status` | 查看当前UART GPIO状态 | 状态 |
| `ota` | 固件在线更新 | 系统 |
| `settings` | 系统设置管理 | 配置 |

## 6. 存储限制和约束

### 6.1 存储限制
- **最大指令数量**: 50个文本指令
- **指令名称长度**: 最大32字符
- **指令内容长度**: 最大500字符
- **存储方式**: NVS (Non-Volatile Storage)

### 6.2 指令名称规则
- 只能包含字母、数字和下划线
- 不能以数字开头
- 区分大小写
- 不能与系统指令重名

### 6.3 字符编码
- 使用UTF-8编码
- 支持中文字符
- 特殊字符需要转义

## 7. 电脑端程序架构设计

### 7.1 推荐技术栈
**Python + tkinter方案（推荐）**：
- **语言**: Python 3.8+
- **UI框架**: tkinter (内置)
- **串口库**: pyserial
- **配置文件**: JSON
- **日志**: logging模块

**优势**：
- 无需额外安装UI框架
- 跨平台兼容性好
- 开发简单快速
- 依赖少，部署方便

### 7.2 项目结构
```
xiaozhi_manager/
├── main.py                 # 主程序入口
├── gui/
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── dialogs.py          # 对话框
│   └── widgets.py          # 自定义控件
├── communication/
│   ├── __init__.py
│   ├── serial_manager.py   # 串口管理
│   ├── protocol.py         # 协议处理
│   └── command_parser.py   # 命令解析
├── data/
│   ├── __init__.py
│   ├── models.py           # 数据模型
│   └── storage.py          # 本地存储
├── utils/
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   └── config.py           # 配置管理
├── resources/
│   ├── icons/              # 图标资源
│   └── config.json         # 默认配置
└── requirements.txt        # 依赖列表
```

### 7.3 核心模块设计

#### 7.3.1 串口管理模块 (serial_manager.py)
```python
class SerialManager:
    def __init__(self):
        self.port = None
        self.baudrate = 115200
        self.connection = None
        self.is_connected = False

    def scan_ports(self) -> List[str]:
        """扫描可用串口"""

    def connect(self, port: str) -> bool:
        """连接串口"""

    def disconnect(self):
        """断开连接"""

    def send_command(self, command: str) -> str:
        """发送命令并等待响应"""

    def set_callback(self, callback):
        """设置数据接收回调"""
```

#### 7.3.2 协议处理模块 (protocol.py)
```python
class ProtocolHandler:
    @staticmethod
    def escape_string(text: str) -> str:
        """字符串转义"""

    @staticmethod
    def unescape_string(text: str) -> str:
        """字符串反转义"""

    @staticmethod
    def parse_response(response: str) -> dict:
        """解析响应"""

    @staticmethod
    def build_command(cmd: str, *params) -> str:
        """构建命令"""
```

## 8. GUI界面设计要求

### 8.1 主窗口布局
```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏: 文件 | 编辑 | 工具 | 帮助                              │
├─────────────────────────────────────────────────────────────┤
│ 工具栏: [连接] [断开] [刷新] [备份] [恢复] [设置]              │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────────────────────────┐ │
│ │   串口连接区     │ │           系统状态区                │ │
│ │ 端口: [COM3 ▼] │ │ 连接状态: ●已连接                   │ │
│ │ 波特率: 115200  │ │ 文本指令: 5/50个                    │ │
│ │ [连接] [断开]   │ │ 系统指令: 13个                      │ │
│ │ [PING测试]      │ │ 空闲内存: 95KB                      │ │
│ └─────────────────┘ └─────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   指令管理区                            │ │
│ │ [文本指令] [系统指令]                                   │ │
│ │ ┌─────────────────────────────────────────────────────┐ │ │
│ │ │ 指令名称 │ 文本内容        │ 操作                    │ │ │
│ │ ├─────────────────────────────────────────────────────┤ │ │
│ │ │ music   │ 播放音乐        │[编辑][删除][执行][测试] │ │ │
│ │ │ weather │ 今天天气怎么样  │[编辑][删除][执行][测试] │ │ │
│ │ └─────────────────────────────────────────────────────┘ │ │
│ │ [添加指令] [批量导入] [批量导出] [全部重置]             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                   日志输出区                            │ │
│ │ 2025-07-29 22:30:15 [INFO] 连接到ESP32成功              │ │
│ │ 2025-07-29 22:30:16 [INFO] 获取指令列表成功              │ │
│ │ 2025-07-29 22:30:20 [INFO] 执行指令 'music' 成功         │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 就绪 | 串口: COM3 | 波特率: 115200 | 指令数: 5      │
└─────────────────────────────────────────────────────────────┘
```

### 8.2 指令编辑对话框
```
┌─────────────────────────────────────────┐
│           添加/编辑文本指令              │
├─────────────────────────────────────────┤
│ 指令名称: [music                    ]   │
│ (只能包含字母、数字、下划线，不能以数字开头) │
│                                         │
│ 文本内容: [播放音乐                 ]   │
│ (最大500字符，支持中文)                 │
│                                         │
│ □ 添加后立即测试执行                    │
│                                         │
│           [确定] [取消] [测试]          │
└─────────────────────────────────────────┘
```

### 8.3 系统指令查看对话框
```
┌─────────────────────────────────────────┐
│              系统指令列表                │
├─────────────────────────────────────────┤
│ ┌─────────────────────────────────────┐ │
│ │ 指令名称 │ 功能描述              │ │ │
│ ├─────────────────────────────────────┤ │ │
│ │ help    │ 显示帮助信息          │ │ │
│ │ ask     │ 发送文本给小智AI      │ │ │
│ │ wifi    │ WiFi网络管理          │ │ │
│ │ 4g      │ 4G网络管理            │ │ │
│ │ reboot  │ 重启设备              │ │ │
│ └─────────────────────────────────────┘ │
│                                         │
│                  [关闭]                 │
└─────────────────────────────────────────┘
```

## 9. 功能模块详细设计

### 9.1 串口通信模块
**类名：** `SerialManager`

**主要方法：**
- `scan_ports()` - 获取可用串口列表
- `connect(port, baudrate=115200)` - 连接串口
- `disconnect()` - 断开连接
- `send_command(command)` - 发送文本命令
- `receive_response(timeout=5)` - 接收文本响应
- `ping_test()` - PING连接测试
- `is_connected()` - 检查连接状态

### 9.2 协议处理模块
**类名：** `ProtocolHandler`

**主要方法：**
- `escape_string(text)` - 字符串转义
- `unescape_string(text)` - 字符串反转义
- `parse_response(response)` - 解析响应
- `build_command(cmd, *params)` - 构建命令
- `validate_command_name(name)` - 验证指令名称

### 9.3 指令管理模块
**类名：** `CommandManager`

**主要方法：**
- `get_text_commands()` - 获取文本指令列表
- `get_system_commands()` - 获取系统指令列表
- `add_command(name, text)` - 添加文本指令
- `modify_command(name, new_text)` - 修改文本指令
- `delete_command(name)` - 删除文本指令
- `execute_command(name)` - 执行指令
- `backup_commands()` - 备份所有指令
- `restore_commands(data)` - 恢复指令
- `reset_commands()` - 重置所有指令

### 9.4 配置管理模块
**类名：** `ConfigManager`

**配置项：**
- 串口设置（端口、波特率115200）
- 界面设置（窗口大小、位置）
- 日志设置（级别、保存路径）
- 自动连接设置
- 备份文件路径

### 9.5 日志管理模块
**类名：** `LogManager`

**功能：**
- 多级别日志记录（DEBUG、INFO、WARN、ERROR）
- 日志文件自动轮转
- 实时日志显示
- 日志搜索和过滤
- 协议通信日志记录

## 10. 实现示例代码

### 10.1 协议处理示例
```python
class ProtocolHandler:
    @staticmethod
    def escape_string(text):
        """字符串转义"""
        return text.replace('\\', '\\\\').replace('|', '\\p').replace('\n', '\\n').replace('\r', '\\r')

    @staticmethod
    def unescape_string(text):
        """字符串反转义"""
        return text.replace('\\p', '|').replace('\\\\', '\\').replace('\\n', '\n').replace('\\r', '\r')

    @staticmethod
    def build_command(cmd, *params):
        """构建命令"""
        if params:
            escaped_params = [ProtocolHandler.escape_string(str(p)) for p in params]
            return f"{cmd}:{':'.join(escaped_params)}\n"
        return f"{cmd}\n"

    @staticmethod
    def parse_response(response):
        """解析响应"""
        response = response.strip()
        if response == "PONG":
            return {"status": "success", "type": "pong"}
        elif response.startswith("OK:"):
            data = response[3:] if len(response) > 3 else ""
            return {"status": "success", "data": data}
        elif response.startswith("ERROR:"):
            error = response[6:] if len(response) > 6 else "unknown error"
            return {"status": "error", "error": error}
        else:
            return {"status": "unknown", "raw": response}
```

### 10.2 串口管理示例
```python
import serial
import serial.tools.list_ports
import threading
import time

class SerialManager:
    def __init__(self):
        self.connection = None
        self.is_connected = False
        self.receive_thread = None
        self.callback = None

    def scan_ports(self):
        """扫描可用串口"""
        ports = []
        for port in serial.tools.list_ports.comports():
            ports.append(port.device)
        return ports

    def connect(self, port, baudrate=115200):
        """连接串口"""
        try:
            self.connection = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1
            )
            self.is_connected = True
            self.start_receive_thread()
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        self.is_connected = False
        if self.connection:
            self.connection.close()
            self.connection = None

    def send_command(self, command):
        """发送命令"""
        if not self.is_connected or not self.connection:
            return None
        try:
            self.connection.write(command.encode('utf-8'))
            return True
        except Exception as e:
            print(f"发送失败: {e}")
            return False

    def ping_test(self):
        """PING测试"""
        if self.send_command("PING\n"):
            # 等待PONG响应
            time.sleep(0.5)
            return True
        return False
```

## 11. 开发要求和规范

### 11.1 依赖库要求
```
# requirements.txt
pyserial>=3.5
tkinter (Python内置)
threading (Python内置)
json (Python内置)
configparser (Python内置)
logging (Python内置)
```

### 11.2 错误处理要求
- **串口连接异常**：提供重连机制和友好提示
- **协议解析错误**：记录原始数据并提示用户
- **超时处理**：设置合理的超时时间（5秒）
- **用户输入验证**：实时验证指令名称和内容格式
- **友好的错误提示**：使用中文提示，避免技术术语

### 11.3 用户体验要求
- **自动保存窗口状态**：记住窗口大小和位置
- **自动重连**：程序启动时尝试连接上次使用的串口
- **操作确认**：删除、重置等危险操作需要确认
- **实时状态显示**：连接状态、指令数量等实时更新
- **快捷键支持**：常用操作提供快捷键

### 11.4 性能要求
- **响应时间**：界面操作响应时间 < 100ms
- **串口通信**：命令响应时间 < 5秒
- **内存占用**：程序运行内存 < 50MB
- **启动时间**：程序启动时间 < 3秒

## 12. 测试指南

### 12.1 功能测试清单
- [ ] **连接测试**：串口扫描、连接、断开、PING测试
- [ ] **指令管理**：添加、修改、删除、查询文本指令
- [ ] **指令执行**：执行文本指令，验证小智响应
- [ ] **批量操作**：备份、恢复、重置功能
- [ ] **错误处理**：网络断开、指令错误、超时等异常情况
- [ ] **界面操作**：所有按钮、菜单、对话框功能

### 12.2 兼容性测试
- [ ] **操作系统**：Windows 10/11, macOS, Linux
- [ ] **Python版本**：Python 3.8+
- [ ] **串口设备**：不同品牌的USB转TTL模块
- [ ] **屏幕分辨率**：1920x1080, 1366x768等常见分辨率

### 12.3 性能测试
- [ ] **响应时间**：界面操作响应 < 100ms
- [ ] **通信延迟**：串口命令响应 < 5秒
- [ ] **内存占用**：运行时内存 < 50MB
- [ ] **稳定性**：长时间运行无内存泄漏

## 13. 部署和分发

### 13.1 Python环境部署
```bash
# 1. 安装Python 3.8+
# 2. 安装依赖
pip install pyserial

# 3. 运行程序
python main.py
```

### 13.2 打包为可执行文件
```bash
# 使用PyInstaller打包
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

### 13.3 发布清单
- [ ] **可执行文件**：Windows .exe文件
- [ ] **用户手册**：操作说明文档
- [ ] **安装指南**：环境配置说明
- [ ] **示例配置**：默认配置文件
- [ ] **更新日志**：版本变更记录

## 14. 开发优先级建议

### 14.1 第一阶段（核心功能）
1. **串口连接管理**：扫描、连接、断开、PING测试
2. **基础协议实现**：命令构建、响应解析、字符转义
3. **文本指令管理**：增删改查基本操作
4. **简单GUI界面**：主窗口、指令列表、基本操作按钮

### 14.2 第二阶段（完善功能）
1. **指令执行测试**：执行指令并显示结果
2. **批量操作**：备份、恢复、重置功能
3. **错误处理**：完善的异常处理和用户提示
4. **配置管理**：保存用户设置和窗口状态

### 14.3 第三阶段（优化体验）
1. **界面美化**：图标、布局优化、主题支持
2. **高级功能**：搜索、过滤、统计等
3. **性能优化**：响应速度、内存占用优化
4. **打包发布**：生成可执行文件和安装包

## 15. 关键技术要点

### 15.1 协议实现要点
- **字符转义**：正确处理特殊字符，避免协议解析错误
- **超时处理**：设置合理的命令超时时间（5秒）
- **错误恢复**：连接断开后的自动重连机制
- **编码处理**：确保UTF-8编码正确处理中文

### 15.2 GUI设计要点
- **响应式布局**：适应不同屏幕尺寸
- **状态反馈**：实时显示连接状态和操作结果
- **操作确认**：危险操作需要用户确认
- **快捷操作**：常用功能提供快捷键

### 15.3 数据安全要点
- **输入验证**：严格验证用户输入的指令名称和内容
- **备份机制**：重要操作前自动备份
- **错误恢复**：操作失败时的回滚机制
- **日志记录**：详细记录操作日志便于调试

## 16. 总结

本文档基于ESP32实际实现的UART指令管理协议，提供了完整的电脑端GUI程序开发指南。

### 16.1 核心特点
- **简化协议**：使用文本格式，易于实现和调试
- **完整功能**：支持文本指令的完整生命周期管理
- **实用性强**：直接对接现有ESP32固件实现
- **扩展性好**：预留了未来功能扩展的空间

### 16.2 开发建议
- **优先实现核心功能**：连接管理和基本指令操作
- **注重用户体验**：友好的界面和错误提示
- **充分测试**：确保各种异常情况的正确处理
- **文档完善**：提供详细的用户使用说明

### 16.3 预期效果
通过本GUI工具，用户可以：
- 方便地管理小智设备的文本指令
- 实时测试指令执行效果
- 批量备份和恢复指令配置
- 远程控制小智设备功能

这将大大提升小智设备的可用性和用户体验。

