@echo off
chcp 65001 > nul
title 小智ESP32指令管理器 - 打包工具
echo ========================================
echo 小智ESP32指令管理器 - 打包工具 v2.0
echo ========================================
echo.
echo 开始打包流程...
echo 当前目录: %CD%
echo.

:: 检查Python是否在PATH中
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Python或Python未添加到PATH
    echo 请确保Python已正确安装并添加到系统PATH
    echo 支持的Python版本: 3.8+
    pause
    exit /b 1
)

echo ✅ 找到Python: 
python --version
echo.

:: 检查主程序文件是否存在
if not exist "main.py" (
    echo ❌ 错误: main.py 文件未找到
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

:: 检查必要的目录结构
if not exist "gui" (
    echo ❌ 错误: gui 目录未找到
    echo 请确保项目结构完整
    pause
    exit /b 1
)

if not exist "core" (
    echo ❌ 错误: core 目录未找到
    echo 请确保项目结构完整
    pause
    exit /b 1
)

echo 📦 安装/更新必要的包...
python -m pip install --upgrade pip
python -m pip install pyinstaller
python -m pip install pyserial
if exist "requirements.txt" (
    python -m pip install -r requirements.txt
)

echo.
echo 🔨 构建可执行文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

:: 清理之前的构建文件
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del /q "*.spec"

:: 创建必要的目录和文件
if not exist "resources" mkdir "resources"
if exist "config.ini" (
    if not exist "resources\config.ini" (
        copy "config.ini" "resources\config.ini" >nul 2>&1
    )
)

:: 使用PyInstaller打包
python -m PyInstaller ^
    --clean ^
    --noconfirm ^
    --onefile ^
    --windowed ^
    --name "小智ESP32指令管理器" ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.filedialog ^
    --hidden-import tkinter.simpledialog ^
    --hidden-import serial ^
    --hidden-import serial.tools.list_ports ^
    --hidden-import logging ^
    --hidden-import threading ^
    --hidden-import json ^
    --hidden-import configparser ^
    --hidden-import datetime ^
    --hidden-import time ^
    --hidden-import re ^
    --hidden-import os ^
    --hidden-import sys ^
    --hidden-import queue ^
    --hidden-import typing ^
    --collect-all serial ^
    --collect-all serial.tools ^
    main.py

echo.
if exist "dist\小智ESP32指令管理器.exe" (
    echo ========================================
    echo 🎉 打包成功！
    echo ========================================
    echo.
    echo 可执行文件位置: dist\小智ESP32指令管理器.exe
    echo 文件大小: 
    for %%f in ("dist\小智ESP32指令管理器.exe") do echo   %%~zf 字节
    echo.
    
    :: 复制配置文件到dist目录
    if exist "config.ini" (
        copy "config.ini" "dist\config.ini" >nul 2>&1
        echo ✅ 已复制配置文件到dist目录
    )
    
    :: 创建logs目录
    if not exist "dist\logs" mkdir "dist\logs"
    echo ✅ 已创建日志目录
    
    echo.
    echo 📋 使用说明:
    echo 1. 可执行文件是独立的，无需安装Python即可运行
    echo 2. 可以将整个dist文件夹复制到任何Windows电脑上使用
    echo 3. 首次运行时可能被杀毒软件拦截，请添加信任
    echo 4. 确保目标电脑有USB转串口驱动程序
    echo.
    echo 📁 打包内容:
    echo   - 主程序 (main.py)
    echo   - GUI界面模块 (gui/*)
    echo   - 核心功能模块 (core/*)
    echo   - 工具模块 (utils/*)
    echo   - 配置文件 (config.ini)
    echo   - 日志目录 (logs/)
    echo.
    echo ✅ 您现在可以分发这个exe文件了！
    echo.
    echo 🚀 要立即测试exe文件吗？(Y/N)
    set /p test_choice=
    if /i "%test_choice%"=="Y" (
        echo 启动测试...
        start "" "dist\小智ESP32指令管理器.exe"
    )
) else (
    echo ========================================
    echo ❌ 打包失败
    echo ========================================
    echo.
    echo 请检查上面的错误信息。
    echo 常见问题:
    echo 1. Python版本不兼容 (需要3.8+)
    echo 2. 缺少必要的包
    echo 3. 磁盘空间不足
    echo 4. 权限不足 (尝试以管理员身份运行)
    echo 5. 杀毒软件阻止了打包过程
    echo.
)

echo.
echo 清理临时文件...
if exist "build" rmdir /s /q "build"

echo.
echo 按任意键退出...
pause > nul
