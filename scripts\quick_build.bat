@echo off
chcp 65001 > nul
title 快速打包 - 小智ESP32指令管理器
echo 🚀 快速打包 - 小智ESP32指令管理器
echo ================================
echo.

:: 检查主程序文件
if not exist "main.py" (
    echo ❌ 错误: main.py 文件未找到
    pause
    exit /b 1
)

echo 📦 正在打包，请稍候...
echo.

:: 清理旧文件
if exist "dist" rmdir /s /q "dist" 2>nul
if exist "build" rmdir /s /q "build" 2>nul

:: 快速打包命令
python -m PyInstaller --onefile --windowed --name "小智ESP32指令管理器" main.py

echo.
if exist "dist\小智ESP32指令管理器.exe" (
    echo ✅ 打包成功！
    echo 📁 文件位置: dist\小智ESP32指令管理器.exe
    echo.
    echo 🎉 可以开始使用了！
) else (
    echo ❌ 打包失败，请使用 build_xiaozhi.bat 进行详细打包
)

echo.
pause
