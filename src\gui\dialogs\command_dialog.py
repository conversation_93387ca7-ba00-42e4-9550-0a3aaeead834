#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指令编辑对话框模块
实现添加/编辑自定义指令的对话框界面
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import logging
from typing import Optional

from core.command_manager import CommandManager
from utils.validators import Validators

class CommandDialog:
    """指令编辑对话框类"""
    
    def __init__(self, parent, command_manager: CommandManager,
                 edit_mode: bool = False, name: str = "", text: str = ""):
        self.parent = parent
        self.command_manager = command_manager
        self.edit_mode = edit_mode
        self.original_name = name
        self.result = False
        self.logger = logging.getLogger(__name__)
        self.dialog = None

        try:
            self.logger.info(f"开始创建{'编辑' if edit_mode else '添加'}指令对话框")

            # 创建对话框窗口
            self.dialog = tk.Toplevel(parent)
            self.dialog.title("编辑文本指令" if edit_mode else "添加文本指令")
            self.dialog.geometry("500x350")
            self.dialog.transient(parent)
            self.dialog.grab_set()
            self.dialog.resizable(False, False)

            # 居中显示
            self.center_window()

            # 变量
            self.name_var = tk.StringVar(value=name)
            self.text_var = tk.StringVar(value=text)
            self.test_after_add_var = tk.BooleanVar(value=False)

            # 创建界面
            self.setup_ui()

            # 绑定事件
            self.dialog.protocol("WM_DELETE_WINDOW", self.cancel_clicked)
            self.dialog.bind('<Return>', lambda e: self.ok_clicked())
            self.dialog.bind('<Escape>', lambda e: self.cancel_clicked())

            # 设置焦点
            if not edit_mode:
                self.name_entry.focus_set()
            else:
                self.text_entry.focus_set()

            self.logger.info("对话框创建完成，开始等待用户操作...")

            # 等待对话框关闭（模态对话框）
            self.dialog.wait_window()

            self.logger.info(f"对话框关闭，最终结果: {self.result}")

        except Exception as e:
            self.logger.error(f"创建对话框时出错: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            self.result = False
            if self.dialog:
                try:
                    self.dialog.destroy()
                except:
                    pass
    
    def center_window(self):
        """居中显示窗口"""
        try:
            self.dialog.update_idletasks()
            
            # 获取窗口尺寸
            width = self.dialog.winfo_width()
            height = self.dialog.winfo_height()
            
            # 获取父窗口位置和尺寸
            parent_x = self.parent.winfo_x()
            parent_y = self.parent.winfo_y()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # 计算居中位置
            x = parent_x + (parent_width - width) // 2
            y = parent_y + (parent_height - height) // 2
            
            self.dialog.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            self.logger.error(f"窗口居中失败: {e}")
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 标题
        title_label = ttk.Label(main_frame,
                               text="编辑文本指令" if self.edit_mode else "添加文本指令",
                               font=("", 12, "bold"))
        title_label.pack(pady=(0, 20))

        # 输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.BOTH, expand=True)

        # 指令名称
        name_frame = ttk.Frame(input_frame)
        name_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(name_frame, text="指令名称:", width=12).pack(side=tk.LEFT)
        self.name_entry = ttk.Entry(name_frame, textvariable=self.name_var, font=("", 10))
        self.name_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        # 如果是编辑模式，禁用名称编辑
        if self.edit_mode:
            self.name_entry.config(state='readonly')

        # 名称提示
        name_tip = ttk.Label(input_frame,
                            text="只能包含字母、数字、下划线，不能以数字开头，长度不超过32字符",
                            font=("", 8), foreground="gray")
        name_tip.pack(fill=tk.X, pady=(0, 10))
        
        # 文本内容
        text_frame = ttk.Frame(input_frame)
        text_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(text_frame, text="文本内容:", width=12).pack(side=tk.LEFT, anchor=tk.N)
        
        text_input_frame = ttk.Frame(text_frame)
        text_input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        self.text_entry = tk.Text(text_input_frame, height=6, wrap=tk.WORD, font=("", 10))
        text_scrollbar = ttk.Scrollbar(text_input_frame, orient=tk.VERTICAL, command=self.text_entry.yview)
        self.text_entry.configure(yscrollcommand=text_scrollbar.set)
        
        self.text_entry.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 设置初始文本内容
        if self.text_var.get():
            self.text_entry.insert(1.0, self.text_var.get())
        
        # 文本提示
        text_tip = ttk.Label(input_frame,
                            text="指令的具体文本内容，长度不超过500字符",
                            font=("", 8), foreground="gray")
        text_tip.pack(fill=tk.X, pady=(0, 15))
        
        # 选项
        options_frame = ttk.Frame(input_frame)
        options_frame.pack(fill=tk.X, pady=(0, 20))
        
        if not self.edit_mode:
            test_check = ttk.Checkbutton(options_frame, 
                                        text="添加后立即测试",
                                        variable=self.test_after_add_var)
            test_check.pack(side=tk.LEFT)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        # 按钮
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        
        if not self.edit_mode:
            ttk.Button(button_frame, text="测试", command=self.test_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        
        self.ok_button = ttk.Button(button_frame, 
                                   text="确定", 
                                   command=self.ok_clicked)
        self.ok_button.pack(side=tk.RIGHT, padx=(5, 0))
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", foreground="red")
        self.status_label.pack(fill=tk.X, pady=(10, 0))
    
    def validate_input(self) -> bool:
        """验证输入数据"""
        try:
            name = self.name_var.get().strip()
            text = self.text_entry.get(1.0, tk.END).strip()

            # 验证指令名称
            if not self.command_manager.validate_command_name(name):
                self.status_label.config(text="指令名称无效：只能包含字母、数字、下划线，不能以数字开头，长度不超过32字符")
                return False

            # 验证指令文本
            if not self.command_manager.validate_command_text(text):
                self.status_label.config(text="指令文本无效：不能为空，长度不超过500字符")
                return False

            self.status_label.config(text="")
            return True

        except Exception as e:
            self.status_label.config(text=f"验证失败: {e}")
            return False
    
    def ok_clicked(self):
        """确定按钮点击事件"""
        try:
            if not self.validate_input():
                return
            
            name = self.name_var.get().strip()
            text = self.text_entry.get(1.0, tk.END).strip()

            # 禁用按钮，显示处理状态
            self.ok_button.config(state=tk.DISABLED, text="处理中...")
            self.status_label.config(text="正在处理，请稍候...", foreground="blue")

            def process_thread():
                try:
                    success = False

                    if self.edit_mode:
                        # 编辑模式
                        success = self.command_manager.modify_command(name, text)
                        action = "修改"
                    else:
                        # 添加模式
                        success = self.command_manager.add_command(name, text)
                        action = "添加"

                    # 在主线程中更新UI
                    self.dialog.after(0, self.process_complete, success, action, name)

                except Exception as e:
                    self.dialog.after(0, self.process_error, str(e))
            
            threading.Thread(target=process_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"处理指令失败: {e}")
            self.status_label.config(text=f"处理失败: {e}", foreground="red")
            self.ok_button.config(state=tk.NORMAL, text="确定")
    
    def process_complete(self, success: bool, action: str, name: str):
        """处理完成回调"""
        try:
            self.ok_button.config(state=tk.NORMAL, text="确定")

            if success:
                self.logger.info(f"指令{action}成功: {name}，设置 result = True")
                self.result = True

                # 如果是添加模式且选择了测试
                if not self.edit_mode and self.test_after_add_var.get():
                    self.test_command(name)
                else:
                    # 不显示弹窗，直接关闭对话框
                    self.logger.info("操作成功，关闭对话框")
                    if self.dialog:
                        self.dialog.destroy()
            else:
                self.status_label.config(text=f"指令{action}失败", foreground="red")
                self.logger.error(f"指令{action}失败: {name}")

        except Exception as e:
            self.logger.error(f"处理完成回调失败: {e}")
    
    def process_error(self, error_msg: str):
        """处理错误回调"""
        self.ok_button.config(state=tk.NORMAL, text="确定")
        self.status_label.config(text=f"处理失败: {error_msg}", foreground="red")
    
    def test_clicked(self):
        """测试按钮点击事件"""
        try:
            if not self.validate_input():
                return
            
            name = self.name_var.get().strip()
            self.test_command(name)
            
        except Exception as e:
            self.logger.error(f"测试指令失败: {e}")
            messagebox.showerror("错误", f"测试指令失败: {e}")
    
    def test_command(self, name: str):
        """测试指令"""
        try:
            def test_thread():
                try:
                    success = self.command_manager.execute_command(name)
                    if success:
                        self.dialog.after(0, lambda: messagebox.showinfo("测试成功", f"指令 '{name}' 执行成功"))
                        if not self.edit_mode:
                            self.dialog.after(0, lambda: self.dialog.destroy())
                            self.result = True
                    else:
                        self.dialog.after(0, lambda: messagebox.showerror("测试失败", f"指令 '{name}' 执行失败"))
                except Exception as e:
                    self.dialog.after(0, lambda: messagebox.showerror("测试错误", f"测试指令时出错: {e}"))
            
            threading.Thread(target=test_thread, daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"测试指令失败: {e}")
            messagebox.showerror("错误", f"测试指令失败: {e}")
    
    def cancel_clicked(self):
        """取消按钮点击事件"""
        self.logger.info("用户点击取消或关闭对话框")
        self.result = False
        if self.dialog:
            self.dialog.destroy()
