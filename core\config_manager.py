#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责应用程序配置的保存和加载
"""

import os
import configparser
import logging
from typing import Any, Dict, Optional

class ConfigManager:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            'serial': {
                'port': '',
                'baudrate': '19200',
                'auto_connect': 'false',
                'last_port': ''
            },
            'window': {
                'width': '800',
                'height': '600',
                'x': '100',
                'y': '100',
                'maximized': 'false'
            },
            'logging': {
                'level': 'INFO',
                'file_path': 'logs/app.log',
                'max_size': '10485760',  # 10MB
                'backup_count': '5'
            },
            'ui': {
                'theme': 'default',
                'font_size': '9',
                'auto_refresh': 'true',
                'refresh_interval': '5'
            }
        }
        
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
                self.logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        try:
            for section, options in self.default_config.items():
                if not self.config.has_section(section):
                    self.config.add_section(section)
                for key, value in options.items():
                    self.config.set(section, key, value)
            
            self.save_config()
            self.logger.info("默认配置创建成功")
            
        except Exception as e:
            self.logger.error(f"创建默认配置失败: {e}")
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_file)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            self.logger.debug("配置文件保存成功")
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
    
    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """
        获取配置值
        
        Args:
            section: 配置节
            key: 配置键
            fallback: 默认值
            
        Returns:
            str: 配置值
        """
        try:
            if not self.config.has_section(section):
                return str(fallback) if fallback is not None else ""
            
            return self.config.get(section, key, fallback=str(fallback) if fallback is not None else "")
            
        except Exception as e:
            self.logger.error(f"获取配置失败 [{section}][{key}]: {e}")
            return str(fallback) if fallback is not None else ""
    
    def get_int(self, section: str, key: str, fallback: int = 0) -> int:
        """获取整数配置值"""
        try:
            value = self.get(section, key, str(fallback))
            return int(value)
        except ValueError:
            return fallback
    
    def get_float(self, section: str, key: str, fallback: float = 0.0) -> float:
        """获取浮点数配置值"""
        try:
            value = self.get(section, key, str(fallback))
            return float(value)
        except ValueError:
            return fallback
    
    def get_bool(self, section: str, key: str, fallback: bool = False) -> bool:
        """获取布尔配置值"""
        try:
            value = self.get(section, key, str(fallback)).lower()
            return value in ('true', '1', 'yes', 'on')
        except:
            return fallback
    
    def set(self, section: str, key: str, value: Any):
        """
        设置配置值
        
        Args:
            section: 配置节
            key: 配置键
            value: 配置值
        """
        try:
            if not self.config.has_section(section):
                self.config.add_section(section)
            
            self.config.set(section, key, str(value))
            
        except Exception as e:
            self.logger.error(f"设置配置失败 [{section}][{key}]: {e}")
    
    def get_section(self, section: str) -> Dict[str, str]:
        """
        获取整个配置节
        
        Args:
            section: 配置节名称
            
        Returns:
            Dict: 配置节的所有键值对
        """
        try:
            if self.config.has_section(section):
                return dict(self.config.items(section))
            else:
                return {}
        except Exception as e:
            self.logger.error(f"获取配置节失败 [{section}]: {e}")
            return {}
    
    def set_section(self, section: str, options: Dict[str, Any]):
        """
        设置整个配置节
        
        Args:
            section: 配置节名称
            options: 配置选项字典
        """
        try:
            if not self.config.has_section(section):
                self.config.add_section(section)
            
            for key, value in options.items():
                self.config.set(section, key, str(value))
                
        except Exception as e:
            self.logger.error(f"设置配置节失败 [{section}]: {e}")
    
    # 便捷方法：串口配置
    def get_serial_port(self) -> str:
        """获取串口端口"""
        return self.get('serial', 'port', '')
    
    def set_serial_port(self, port: str):
        """设置串口端口"""
        self.set('serial', 'port', port)
        self.set('serial', 'last_port', port)
    
    def get_serial_baudrate(self) -> int:
        """获取串口波特率"""
        return self.get_int('serial', 'baudrate', 19200)
    
    def set_serial_baudrate(self, baudrate: int):
        """设置串口波特率"""
        self.set('serial', 'baudrate', baudrate)
    
    def get_auto_connect(self) -> bool:
        """获取自动连接设置"""
        return self.get_bool('serial', 'auto_connect', False)
    
    def set_auto_connect(self, auto_connect: bool):
        """设置自动连接"""
        self.set('serial', 'auto_connect', auto_connect)
    
    def get_last_port(self) -> str:
        """获取上次使用的端口"""
        return self.get('serial', 'last_port', '')
    
    # 便捷方法：窗口配置
    def get_window_geometry(self) -> tuple:
        """获取窗口几何信息 (width, height, x, y)"""
        width = self.get_int('window', 'width', 800)
        height = self.get_int('window', 'height', 600)
        x = self.get_int('window', 'x', 100)
        y = self.get_int('window', 'y', 100)
        return (width, height, x, y)
    
    def set_window_geometry(self, width: int, height: int, x: int, y: int):
        """设置窗口几何信息"""
        self.set('window', 'width', width)
        self.set('window', 'height', height)
        self.set('window', 'x', x)
        self.set('window', 'y', y)
    
    def get_window_maximized(self) -> bool:
        """获取窗口最大化状态"""
        return self.get_bool('window', 'maximized', False)
    
    def set_window_maximized(self, maximized: bool):
        """设置窗口最大化状态"""
        self.set('window', 'maximized', maximized)
    
    # 便捷方法：日志配置
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get('logging', 'level', 'INFO')
    
    def get_log_file_path(self) -> str:
        """获取日志文件路径"""
        return self.get('logging', 'file_path', 'logs/app.log')
