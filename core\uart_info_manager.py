"""
UART设备信息管理器
负责查询和解析ESP32设备的系统信息
"""

import json
import logging
import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable


class UARTInfoManager:
    """UART设备信息管理器"""
    
    def __init__(self, serial_comm):
        """
        初始化UART信息管理器
        
        Args:
            serial_comm: 串口通信对象
        """
        self.serial_comm = serial_comm
        self.logger = logging.getLogger(__name__)
        
        # 信息缓存
        self.cached_info = {}
        self.last_update_time = {}
        
        # 自动查询相关
        self.auto_query_enabled = False
        self.auto_query_interval = 5  # 默认5秒
        self.auto_query_thread = None
        self.auto_query_callback = None
        
        # 支持的查询命令
        self.query_commands = {
            'system': 'GET_SYSTEM_INFO',
            'network': 'GET_NETWORK_INFO', 
            'audio': 'GET_AUDIO_INFO',
            'hardware': 'GET_HARDWARE_INFO'
        }
        
    def query_system_info(self) -> Optional[Dict[str, Any]]:
        """
        查询完整系统信息

        Returns:
            Dict: 系统信息字典，失败返回None
        """
        try:
            self.logger.info("查询完整系统信息...")

            # 发送命令并使用新的JSON响应处理方法
            if not self.serial_comm.is_connected or not self.serial_comm.serial_port:
                self.logger.error("设备未连接")
                return None

            try:
                # 清空响应队列
                self.serial_comm._clear_response_queue()

                # 构建并发送命令
                command_str = self.serial_comm.protocol.build_command("GET_SYSTEM_INFO")
                command_bytes = command_str.encode('utf-8')

                self.serial_comm.serial_port.write(command_bytes)
                self.logger.info(f"发送命令: {command_str.strip()}")

                # ESP32使用新标记格式，使用标记格式处理方法
                response = self.serial_comm._wait_for_complete_response_with_markers("GET_SYSTEM_INFO", timeout=10)
            except Exception as e:
                self.logger.error(f"发送命令失败: {e}")
                return None

            if response and response.get("status") == "success":
                # 获取完整的JSON数据
                json_data = response.get("data", {})

                if json_data and isinstance(json_data, dict):
                    # 验证数据完整性
                    has_audio = 'audio' in json_data and 'volume' in json_data.get('audio', {})
                    has_hardware = 'hardware' in json_data and 'chip' in json_data.get('hardware', {})
                    has_system = 'system' in json_data

                    if has_audio or has_hardware or has_system:
                        # 更新缓存
                        self.cached_info['system'] = json_data
                        self.last_update_time['system'] = datetime.now()

                        self.logger.info(f"系统信息查询成功（解析完整JSON数据）- 音频:{has_audio}, 硬件:{has_hardware}, 系统:{has_system}")
                        if has_audio:
                            volume = json_data.get('audio', {}).get('volume', 0)
                            self.logger.info(f"获取到实时音量数据: {volume}")
                        return json_data
                    else:
                        self.logger.warning(f"JSON数据不完整，缺少关键字段: {list(json_data.keys())}")
                else:
                    self.logger.warning(f"收到的JSON数据格式不正确: {type(json_data)}, 内容: {json_data}")

            # 所有方法都失败，返回None
            self.logger.warning("所有方法都失败，无法获取系统信息")
            return None

        except Exception as e:
            self.logger.error(f"查询系统信息异常: {e}")
            return None

    def _collect_complete_json(self, first_data: str, timeout: int = 8) -> Optional[Dict[str, Any]]:
        """
        收集完整的JSON数据（处理分段传输）

        Args:
            first_data: 第一段数据
            timeout: 超时时间（秒）

        Returns:
            Dict: 解析后的JSON数据，失败返回None
        """
        try:
            # 去掉"OK:"前缀
            json_buffer = first_data[3:] if first_data.startswith("OK:") else first_data
            self.logger.info(f"开始收集JSON数据，初始数据: {json_buffer[:100]}...")

            # 计算大括号数量
            brace_count = 0
            for char in json_buffer:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1

            self.logger.info(f"初始大括号计数: {brace_count}")

            # 如果第一段数据就是完整的JSON，直接返回
            if brace_count == 0 and json_buffer.strip():
                try:
                    # 清理JSON数据
                    cleaned_json = self._clean_json_data(json_buffer)
                    json_data = json.loads(cleaned_json)
                    self.logger.info(f"第一段数据就是完整JSON")
                    return json_data
                except json.JSONDecodeError as e:
                    self.logger.warning(f"第一段JSON解析失败: {e}")
                    pass  # 继续收集更多数据

            # 继续收集剩余数据
            start_time = time.time()
            collected_lines = 0

            while time.time() - start_time < timeout:
                try:
                    # 检查是否有更多数据
                    if self.serial_comm.serial_port and self.serial_comm.serial_port.in_waiting > 0:
                        line = self.serial_comm.serial_port.readline().decode('utf-8', errors='ignore').strip()

                        if line:
                            collected_lines += 1
                            self.logger.info(f"收集JSON片段 #{collected_lines}: {line}")
                            json_buffer += line

                            # 更新大括号计数
                            for char in line:
                                if char == '{':
                                    brace_count += 1
                                elif char == '}':
                                    brace_count -= 1

                            self.logger.debug(f"当前大括号计数: {brace_count}")

                            # 如果大括号匹配，尝试解析JSON
                            if brace_count == 0:
                                try:
                                    # 清理JSON数据
                                    cleaned_json = self._clean_json_data(json_buffer)
                                    json_data = json.loads(cleaned_json)
                                    self.logger.info(f"成功收集完整JSON数据，大小: {len(json_buffer)} 字符，共收集 {collected_lines} 行")
                                    return json_data
                                except json.JSONDecodeError as e:
                                    self.logger.warning(f"JSON解析失败: {e}")
                                    self.logger.debug(f"问题JSON数据: {json_buffer}")
                                    break
                    else:
                        # 如果没有更多数据，等待一下
                        time.sleep(0.05)

                except Exception as e:
                    self.logger.error(f"收集JSON数据失败: {e}")
                    break

            # 如果超时或失败，尝试修复并解析收集到的数据
            if json_buffer:
                self.logger.warning(f"JSON数据收集不完整，收集到: {len(json_buffer)} 字符，共 {collected_lines} 行")
                self.logger.info(f"尝试修复不完整的JSON数据...")

                # 尝试修复JSON数据
                fixed_json = self._try_fix_incomplete_json(json_buffer)
                if fixed_json:
                    try:
                        json_data = json.loads(fixed_json)
                        self.logger.info("成功修复并解析不完整的JSON数据")
                        return json_data
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"修复后的JSON仍然无法解析: {e}")

                self.logger.debug(f"不完整的JSON数据: {json_buffer[:500]}...")

            return None

        except Exception as e:
            self.logger.error(f"收集完整JSON数据失败: {e}")
            return None

    def _clean_json_data(self, json_str: str) -> str:
        """
        清理JSON数据，移除无效字符和格式问题

        Args:
            json_str: 原始JSON字符串

        Returns:
            str: 清理后的JSON字符串
        """
        try:
            # 移除控制字符和不可见字符
            import re
            cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', json_str)

            # 修复常见的JSON格式问题
            cleaned = cleaned.replace('\t', ' ')  # 替换制表符
            cleaned = re.sub(r'\s+', ' ', cleaned)  # 合并多个空格
            cleaned = cleaned.strip()

            # 修复可能的引号问题
            cleaned = re.sub(r'([{,]\s*)"([^"]*)"(\s*:\s*)"([^"]*)"', r'\1"\2":\3"\4"', cleaned)

            return cleaned
        except Exception as e:
            self.logger.warning(f"清理JSON数据失败: {e}")
            return json_str

    def _try_fix_incomplete_json(self, json_str: str) -> Optional[str]:
        """
        尝试修复不完整的JSON数据

        Args:
            json_str: 不完整的JSON字符串

        Returns:
            str: 修复后的JSON字符串，失败返回None
        """
        try:
            # 清理数据
            cleaned = self._clean_json_data(json_str)

            # 计算大括号数量
            open_braces = cleaned.count('{')
            close_braces = cleaned.count('}')

            # 如果缺少结束大括号，尝试添加
            if open_braces > close_braces:
                missing_braces = open_braces - close_braces
                fixed = cleaned + '}' * missing_braces
                self.logger.info(f"尝试添加 {missing_braces} 个结束大括号")
                return fixed

            # 如果有多余的开始大括号，尝试移除
            elif close_braces > open_braces:
                # 这种情况比较复杂，暂时返回None
                self.logger.warning("检测到多余的结束大括号，无法自动修复")
                return None

            # 大括号数量匹配，可能是其他格式问题
            return cleaned

        except Exception as e:
            self.logger.error(f"修复JSON数据失败: {e}")
            return None

    def _extract_data_from_raw_response(self, raw_data: str) -> Optional[Dict[str, Any]]:
        """
        从原始响应数据中提取信息

        Args:
            raw_data: 原始响应数据

        Returns:
            Dict: 提取的数据字典，失败返回None
        """
        try:
            import re

            # 初始化数据结构
            extracted_data = {
                "hardware": {},
                "network": {"wifi": {}, "4g": {}},
                "audio": {},
                "system": {}
            }

            # 提取硬件信息
            chip_match = re.search(r'"chip":\s*"([^"]*)"', raw_data)
            if chip_match:
                extracted_data["hardware"]["chip"] = chip_match.group(1)

            version_match = re.search(r'"version":\s*"([^"]*)"', raw_data)
            if version_match:
                extracted_data["hardware"]["version"] = version_match.group(1)

            # 提取4G信息
            iccid_match = re.search(r'"iccid":\s*"([^"]*)"', raw_data)
            if iccid_match:
                extracted_data["network"]["4g"]["iccid"] = iccid_match.group(1)
                extracted_data["network"]["4g"]["status"] = "connected"

            imei_match = re.search(r'"imei":\s*"([^"]*)"', raw_data)
            if imei_match:
                extracted_data["network"]["4g"]["imei"] = imei_match.group(1)

            # 提取音频信息
            volume_match = re.search(r'"volume":\s*(\d+)', raw_data)
            if volume_match:
                extracted_data["audio"]["volume"] = int(volume_match.group(1))

            mic_match = re.search(r'"mic_status":\s*"([^"]*)"', raw_data)
            if mic_match:
                extracted_data["audio"]["mic_status"] = mic_match.group(1)

            # 提取系统信息
            tasks_match = re.search(r'"tasks":\s*(\d+)', raw_data)
            if tasks_match:
                extracted_data["system"]["tasks"] = int(tasks_match.group(1))

            flash_match = re.search(r'"flash":\s*"([^"]*)"', raw_data)
            if flash_match:
                extracted_data["system"]["flash"] = flash_match.group(1)

            # 检查是否提取到了有用的数据
            has_data = any(
                bool(section) for section in [
                    extracted_data["hardware"],
                    extracted_data["network"]["4g"],
                    extracted_data["audio"],
                    extracted_data["system"]
                ]
            )

            if has_data:
                self.logger.info(f"从原始数据中提取到信息: {extracted_data}")
                return extracted_data
            else:
                return None

        except Exception as e:
            self.logger.error(f"从原始数据提取信息失败: {e}")
            return None



    def _get_complete_json_response(self, command: str, timeout: int = 10) -> Optional[Dict[str, Any]]:
        """
        获取完整的JSON响应数据（处理分段传输）

        Args:
            command: 要发送的命令
            timeout: 超时时间（秒）

        Returns:
            Dict: 解析后的JSON数据，失败返回None
        """
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.error("设备未连接")
                return None

            # 使用现有的串口通信方法发送命令
            response = self.serial_comm.send_command(command)

            if not response or response.get("status") != "success":
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"命令发送失败: {error_msg}")
                return None

            # 获取第一段数据
            first_data = response.get("data", "")
            if not first_data:
                self.logger.error("未收到任何数据")
                return None

            # 收集所有响应数据
            json_buffer = ""
            start_time = time.time()
            brace_count = 0
            started = False

            # 处理第一段数据
            if first_data.startswith("OK:{") or first_data.startswith("{"):
                started = True
                if first_data.startswith("OK:"):
                    first_data = first_data[3:]  # 去掉"OK:"前缀
                json_buffer += first_data

                # 计算大括号数量
                for char in first_data:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1

            # 如果第一段数据就是完整的JSON，直接返回
            if brace_count == 0 and json_buffer.strip():
                try:
                    json_data = json.loads(json_buffer)
                    self.logger.info(f"第一段数据就是完整JSON，大小: {len(json_buffer)} 字符")
                    return json_data
                except json.JSONDecodeError:
                    pass  # 继续收集更多数据

            # 继续收集剩余数据
            while time.time() - start_time < timeout and started:
                try:
                    # 读取串口数据
                    if self.serial_comm.serial_port and self.serial_comm.serial_port.in_waiting > 0:
                        line = self.serial_comm.serial_port.readline().decode('utf-8', errors='ignore').strip()

                        if line:
                            self.logger.debug(f"收到数据片段: {line}")
                            json_buffer += line

                            # 计算大括号数量来判断JSON是否完整
                            for char in line:
                                if char == '{':
                                    brace_count += 1
                                elif char == '}':
                                    brace_count -= 1

                            # 如果大括号匹配，说明JSON完整
                            if brace_count == 0 and json_buffer.strip():
                                try:
                                    # 尝试解析JSON
                                    json_data = json.loads(json_buffer)
                                    self.logger.info(f"成功解析完整JSON数据，大小: {len(json_buffer)} 字符")
                                    return json_data
                                except json.JSONDecodeError as e:
                                    self.logger.warning(f"JSON解析失败，继续收集数据: {e}")
                                    continue

                    time.sleep(0.01)  # 短暂等待

                except Exception as e:
                    self.logger.error(f"读取串口数据失败: {e}")
                    break

            # 超时或失败
            if json_buffer:
                self.logger.warning(f"JSON数据不完整或解析失败，收集到的数据: {json_buffer[:200]}...")
            else:
                self.logger.warning(f"未收到任何JSON数据")

            return None

        except Exception as e:
            self.logger.error(f"获取完整JSON响应失败: {e}")
            return None
    
    def query_network_info(self) -> Optional[Dict[str, Any]]:
        """
        查询网络信息
        
        Returns:
            Dict: 网络信息字典，失败返回None
        """
        try:
            self.logger.info("查询网络信息...")
            response = self.serial_comm.send_command("GET_NETWORK_INFO")
            
            if response and response.get("status") == "success":
                data = response.get("data", "")

                try:
                    if data.startswith("OK:"):
                        json_str = data[3:]
                    elif data.startswith("{"):
                        json_str = data
                    else:
                        self.logger.warning(f"网络信息响应格式不识别: {data[:50]}...")
                        return None

                    info = json.loads(json_str)

                    self.cached_info['network'] = info
                    self.last_update_time['network'] = datetime.now()

                    self.logger.info("网络信息查询成功")
                    return info

                except json.JSONDecodeError as e:
                    self.logger.error(f"网络信息JSON解析失败: {e}")
                    return None
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"网络信息查询失败: {error_msg}")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"网络信息JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"查询网络信息异常: {e}")
            return None
    
    def query_audio_info(self) -> Optional[Dict[str, Any]]:
        """
        查询音频信息
        
        Returns:
            Dict: 音频信息字典，失败返回None
        """
        try:
            self.logger.info("查询音频信息...")
            response = self.serial_comm.send_command("GET_AUDIO_INFO")
            
            if response and response.get("status") == "success":
                data = response.get("data", "")
                if data.startswith("OK:"):
                    json_str = data[3:]
                    info = json.loads(json_str)
                    
                    self.cached_info['audio'] = info
                    self.last_update_time['audio'] = datetime.now()
                    
                    self.logger.info("音频信息查询成功")
                    return info
                else:
                    self.logger.warning(f"音频信息查询响应格式错误: {data}")
                    return None
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"音频信息查询失败: {error_msg}")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"音频信息JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"查询音频信息异常: {e}")
            return None
    
    def query_hardware_info(self) -> Optional[Dict[str, Any]]:
        """
        查询硬件信息
        
        Returns:
            Dict: 硬件信息字典，失败返回None
        """
        try:
            self.logger.info("查询硬件信息...")
            response = self.serial_comm.send_command("GET_HARDWARE_INFO")
            
            if response and response.get("status") == "success":
                data = response.get("data", "")
                if data.startswith("OK:"):
                    json_str = data[3:]
                    info = json.loads(json_str)
                    
                    self.cached_info['hardware'] = info
                    self.last_update_time['hardware'] = datetime.now()
                    
                    self.logger.info("硬件信息查询成功")
                    return info
                else:
                    self.logger.warning(f"硬件信息查询响应格式错误: {data}")
                    return None
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"硬件信息查询失败: {error_msg}")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"硬件信息JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"查询硬件信息异常: {e}")
            return None
    
    def get_cached_info(self, info_type: str = 'system') -> Optional[Dict[str, Any]]:
        """
        获取缓存的信息
        
        Args:
            info_type: 信息类型 ('system', 'network', 'audio', 'hardware')
            
        Returns:
            Dict: 缓存的信息，不存在返回None
        """
        return self.cached_info.get(info_type)
    
    def start_auto_query(self, callback: Callable = None, interval: int = 5):
        """
        启动自动查询
        
        Args:
            callback: 查询结果回调函数
            interval: 查询间隔（秒）
        """
        try:
            if self.auto_query_enabled:
                self.logger.warning("自动查询已经在运行")
                return
            
            self.auto_query_enabled = True
            self.auto_query_interval = interval
            self.auto_query_callback = callback
            
            self.auto_query_thread = threading.Thread(target=self._auto_query_worker, daemon=True)
            self.auto_query_thread.start()
            
            self.logger.info(f"自动查询已启动，间隔: {interval}秒")
            
        except Exception as e:
            self.logger.error(f"启动自动查询失败: {e}")
    
    def stop_auto_query(self):
        """停止自动查询"""
        try:
            if not self.auto_query_enabled:
                self.logger.warning("自动查询未在运行")
                return
            
            self.auto_query_enabled = False
            
            if self.auto_query_thread and self.auto_query_thread.is_alive():
                self.auto_query_thread.join(timeout=2)
            
            self.logger.info("自动查询已停止")
            
        except Exception as e:
            self.logger.error(f"停止自动查询失败: {e}")
    
    def _auto_query_worker(self):
        """自动查询工作线程"""
        while self.auto_query_enabled:
            try:
                if self.serial_comm.is_port_connected():
                    # 查询系统信息
                    info = self.query_system_info()
                    
                    # 如果有回调函数，调用它
                    if self.auto_query_callback and info:
                        try:
                            self.auto_query_callback(info)
                        except Exception as e:
                            self.logger.error(f"自动查询回调函数执行失败: {e}")
                
                # 等待指定间隔
                for _ in range(self.auto_query_interval):
                    if not self.auto_query_enabled:
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"自动查询工作线程异常: {e}")
                time.sleep(5)  # 出错时等待5秒再重试
    
    def format_uptime(self, seconds: int) -> str:
        """
        格式化运行时间
        
        Args:
            seconds: 运行时间（秒）
            
        Returns:
            str: 格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds}秒"
        elif seconds < 3600:
            minutes = seconds // 60
            secs = seconds % 60
            return f"{minutes}分{secs}秒"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours}小时{minutes}分钟"
    
    def format_memory(self, bytes_value: int) -> str:
        """
        格式化内存大小
        
        Args:
            bytes_value: 字节数
            
        Returns:
            str: 格式化的内存字符串
        """
        if bytes_value >= 1024 * 1024:
            return f"{bytes_value / (1024 * 1024):.1f}MB"
        elif bytes_value >= 1024:
            return f"{bytes_value / 1024:.1f}KB"
        else:
            return f"{bytes_value}B"
    
    def get_signal_icon(self, rssi: int) -> str:
        """
        根据信号强度返回图标
        
        Args:
            rssi: 信号强度值
            
        Returns:
            str: 信号强度图标
        """
        if rssi >= -50:
            return "📶"  # 强信号
        elif rssi >= -70:
            return "📶"  # 中等信号  
        elif rssi >= -85:
            return "📶"  # 弱信号
        else:
            return "📵"  # 很弱或无信号
