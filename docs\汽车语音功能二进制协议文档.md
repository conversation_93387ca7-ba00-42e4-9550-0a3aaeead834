# 汽车语音功能二进制协议文档

## 📋 目录
- [1. 协议概述](#1-协议概述)
- [2. 数据结构定义](#2-数据结构定义)
- [3. 命令协议](#3-命令协议)
- [4. 通信流程](#4-通信流程)
- [5. 错误处理](#5-错误处理)
- [6. 电脑端UI设计建议](#6-电脑端ui设计建议)
- [7. 实现示例](#7-实现示例)

## 1. 协议概述

### 1.1 设计目标
- **高效传输**: 二进制格式，数据量从3KB降至276字节
- **可靠通信**: 校验和+CRC16双重校验
- **扩展性强**: 支持未来功能扩展
- **跨平台兼容**: 支持串口和蓝牙传输

### 1.2 协议特点
- **魔数**: `0x5A5A` 用于数据包识别
- **版本控制**: 支持协议版本演进
- **命令响应**: 每个命令都有对应响应
- **状态码**: 详细的错误状态反馈

### 1.3 支持的语音功能 (17项)

| ID | 功能名称 | 默认状态 | 可配置参数 |
|---|---|---|---|
| 1 | ACC开启欢迎语 | 启用 | 延迟时间(3秒) |
| 2 | R档倒车提醒 | 启用 | 无 |
| 3 | D档起步提醒 | 启用 | 无 |
| 4-7 | 车门开启提醒 | 启用 | 无 |
| 8-11 | 车门未关警告 | 启用 | 车速阈值(10km/h) |
| 12 | 停车未熄火提醒 | 启用 | 停车时间(60分钟)、间隔(60分钟)、次数(3次) |
| 13 | 方向盘预警 | 启用 | 车速(60km/h)、转角(15度)、持续(20秒) |
| 14 | 疲劳驾驶提醒 | 启用 | 车速(30km/h)、时间(2小时) |
| 15 | 熄火物品提醒 | 启用 | 无 |
| 16 | 方向盘未回正 | 启用 | 转角阈值(10度) |
| 17 | 转向灯持续过长 | 启用 | 车速(30km/h)、持续(20秒)、间隔(10秒)、次数(3次) |

## 2. 数据结构定义

### 2.1 协议头部结构
```c
typedef struct {
    uint16_t magic;           // 魔数 0x5A5A
    uint8_t  version;         // 协议版本 0x01
    uint8_t  command;         // 命令类型
    uint16_t length;          // 数据长度
    uint16_t checksum;        // 简单校验和
} __attribute__((packed)) car_voice_protocol_header_t;
```

### 2.2 配置数据结构 (16字节)
```c
typedef struct {
    uint8_t  voice_id;        // 语音ID (1-17)
    uint8_t  flags;           // bit0:enabled, bit1-2:priority
    uint16_t cooldown_ms;     // 冷却时间（秒）
    uint16_t param1;          // 参数1
    uint16_t param2;          // 参数2
    uint16_t param3;          // 参数3
    uint16_t param4;          // 参数4
    uint16_t param5;          // 参数5
    uint16_t reserved;        // 保留字段
} __attribute__((packed)) car_voice_config_compact_t;
```

### 2.3 完整数据包结构
```c
typedef struct {
    car_voice_protocol_header_t header;    // 8字节
    union {
        car_voice_all_configs_t all_configs;
        car_voice_single_config_t single_config;
        car_voice_test_play_t test_play;
        car_voice_status_t status;
        uint8_t raw_data[512];
    } data;
    uint16_t crc16;           // CRC16校验
} __attribute__((packed)) car_voice_protocol_packet_t;
```

## 3. 命令协议

### 3.1 命令类型定义
```c
#define CMD_CAR_VOICE_GET_ALL_CONFIG    0x10    // 获取所有配置
#define CMD_CAR_VOICE_SET_ALL_CONFIG    0x11    // 设置所有配置
#define CMD_CAR_VOICE_GET_SINGLE_CONFIG 0x12    // 获取单个配置
#define CMD_CAR_VOICE_SET_SINGLE_CONFIG 0x13    // 设置单个配置
#define CMD_CAR_VOICE_RESET_CONFIG      0x14    // 重置为默认配置
#define CMD_CAR_VOICE_TEST_PLAY         0x15    // 测试播放语音
#define CMD_CAR_VOICE_GET_STATUS        0x16    // 获取系统状态
```

### 3.2 响应状态码
```c
#define RESP_CAR_VOICE_SUCCESS          0x00    // 成功
#define RESP_CAR_VOICE_ERROR_INVALID    0x01    // 无效参数
#define RESP_CAR_VOICE_ERROR_STORAGE    0x02    // 存储错误
#define RESP_CAR_VOICE_ERROR_BUSY       0x03    // 系统忙
#define RESP_CAR_VOICE_ERROR_NOT_FOUND  0x04    // 配置未找到
```

### 3.3 UART命令字符串
```c
#define CAR_VOICE_CMD_GET_ALL_CONFIG    "GET_CAR_VOICE_CONFIG"
#define CAR_VOICE_CMD_SET_ALL_CONFIG    "SET_CAR_VOICE_CONFIG"
#define CAR_VOICE_CMD_RESET_CONFIG      "RESET_CAR_VOICE_CONFIG"
#define CAR_VOICE_CMD_TEST_VOICE        "TEST_CAR_VOICE"
#define CAR_VOICE_CMD_GET_STATUS        "GET_CAR_VOICE_STATUS"
```

## 4. 通信流程

### 4.1 获取所有配置流程
```
电脑端 → ESP32: GET_CAR_VOICE_CONFIG
ESP32 → 电脑端: CAR_VOICE_CONFIG_START
ESP32 → 电脑端: OK:
ESP32 → 电脑端: [十六进制配置数据]
ESP32 → 电脑端: CAR_VOICE_CONFIG_END
```

**数据格式示例**:
```
CAR_VOICE_CONFIG_START
OK:
11000000010001001E000A000000000000000000000000000200010005000000000000000000000000000000030001000500000000000000000000000000000004000100030000000000000000000000000000000005000100030000000000000000000000000000000006000100030000000000000000000000000000000007000100030000000000000000000000000000000008000200010000000A000000000000000000000000000009000200010000000A00000000000000000000000000000A000200010000000A00000000000000000000000000000B000200010000000A00000000000000000000000000000C0001000E10003C003C000300000000000000000000000D000200301E003C000F001400000000000000000000000E00020048001E000200000000000000000000000000000F0001000A000000000000000000000000000000000010000100050000000A000000000000000000000000001100010014001E001400000A000300000000000000000000
CAR_VOICE_CONFIG_END
```

### 4.2 设置所有配置流程
```
电脑端 → ESP32: SET_CAR_VOICE_CONFIG [十六进制数据]
ESP32 → 电脑端: CAR_VOICE_CONFIG_START
ESP32 → 电脑端: OK: (成功) 或 ERROR: [错误信息] (失败)
ESP32 → 电脑端: CAR_VOICE_CONFIG_END
```

### 4.3 测试语音播放流程
```
电脑端 → ESP32: TEST_CAR_VOICE 1
ESP32 → 电脑端: CAR_VOICE_CONFIG_START
ESP32 → 电脑端: OK: (成功) 或 ERROR: [错误信息] (失败)
ESP32 → 电脑端: CAR_VOICE_CONFIG_END
```

### 4.4 重置配置流程
```
电脑端 → ESP32: RESET_CAR_VOICE_CONFIG
ESP32 → 电脑端: CAR_VOICE_CONFIG_START
ESP32 → 电脑端: OK: (成功) 或 ERROR: [错误信息] (失败)
ESP32 → 电脑端: CAR_VOICE_CONFIG_END
```

## 5. 错误处理

### 5.1 常见错误类型
- **无效参数**: 参数超出范围或格式错误
- **存储错误**: NVS读写失败
- **系统忙**: 设备正在处理其他请求
- **配置未找到**: 指定的语音ID不存在

### 5.2 错误响应格式
```
CAR_VOICE_CONFIG_START
ERROR: [具体错误信息]
CAR_VOICE_CONFIG_END
```

### 5.3 超时处理
- **命令超时**: 5秒无响应视为超时
- **重试机制**: 最多重试3次
- **连接检测**: 定期发送心跳包

## 6. 电脑端UI设计建议

### 6.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│                    汽车语音播报配置管理 v1.0                   │
├─────────────────────────────────────────────────────────────┤
│ 连接设置: [COM3 ▼] [115200 ▼] [连接] [断开] 状态: ●已连接    │
├─────────────────────────────────────────────────────────────┤
│ 操作: [读取配置] [保存配置] [重置默认] [导入] [导出]          │
├─────────────────────────────────────────────────────────────┤
│ 功能分类: [全部] [车门相关] [档位相关] [驾驶安全] [系统提醒]  │
├─────────────────────────────────────────────────────────────┤
│ ┌─ 001 - ACC开启欢迎语 ─────────────────────────────────────┐ │
│ │ [✓] 启用  优先级: [普通▼]  冷却时间: [30] 秒              │ │
│ │ 延迟播放: [3] 秒                                          │ │
│ │ [测试播放] [重置默认]                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─ 013 - 方向盘预警 ───────────────────────────────────────┐ │
│ │ [✓] 启用  优先级: [高▼]    冷却时间: [30] 秒              │ │
│ │ 车速阈值: [60] km/h  转角阈值: [15] 度                    │ │
│ │ 持续时间: [20] 秒                                         │ │
│ │ [测试播放] [重置默认]                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│ ┌─ 017 - 转向灯持续过长 ───────────────────────────────────┐ │
│ │ [✓] 启用  优先级: [普通▼]  冷却时间: [20] 秒              │ │
│ │ 车速阈值: [30] km/h  持续时间: [20] 秒                    │ │
│ │ 提醒间隔: [10] 秒    最大次数: [3] 次                     │ │
│ │ [测试播放] [重置默认]                                      │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 统计信息: 接收包:156 | 发送包:89 | 错误:0 | 最后更新:23:15:32│
└─────────────────────────────────────────────────────────────┘
```

### 6.2 功能分组建议
1. **车门相关** (004-011): 车门开启提醒、车门未关警告
2. **档位相关** (002-003): R档倒车、D档起步
3. **驾驶安全** (013-014,016-017): 方向盘预警、疲劳驾驶、转向灯等
4. **系统提醒** (001,012,015): 欢迎语、停车提醒、物品提醒

### 6.3 参数输入控件建议
- **开关**: 复选框 (启用/禁用)
- **优先级**: 下拉框 (低/普通/高)
- **时间**: 数字输入框 + 单位标签
- **车速**: 数字输入框 + "km/h" 标签
- **角度**: 数字输入框 + "度" 标签
- **次数**: 数字输入框 + "次" 标签

### 6.4 实时反馈设计
- **连接状态**: 绿色●已连接 / 红色●未连接
- **操作进度**: 进度条显示读取/保存进度
- **错误提示**: 红色文字显示错误信息
- **成功提示**: 绿色文字显示操作成功

## 7. 实现示例

### 7.1 Python客户端示例
```python
import serial
import struct

class CarVoiceConfigClient:
    def __init__(self, port, baudrate=115200):
        self.serial = serial.Serial(port, baudrate, timeout=5)
    
    def get_all_configs(self):
        # 发送命令
        self.serial.write(b"GET_CAR_VOICE_CONFIG\n")
        
        # 读取响应
        response = []
        while True:
            line = self.serial.readline().decode().strip()
            if line == "CAR_VOICE_CONFIG_END":
                break
            response.append(line)
        
        # 解析十六进制数据
        if len(response) >= 3 and response[1] == "OK:":
            hex_data = response[2]
            return self.parse_hex_config(hex_data)
        return None
    
    def parse_hex_config(self, hex_data):
        # 将十六进制字符串转换为二进制数据
        binary_data = bytes.fromhex(hex_data)
        
        # 解析配置数量
        config_count = binary_data[0]
        configs = {}
        
        # 解析每个配置 (16字节)
        offset = 4  # 跳过count和padding
        for i in range(config_count):
            config_data = binary_data[offset:offset+16]
            config = self.parse_single_config(config_data)
            configs[config['voice_id']] = config
            offset += 16
        
        return configs
    
    def parse_single_config(self, data):
        voice_id, flags, cooldown, p1, p2, p3, p4, p5, _ = \
            struct.unpack('<BBHHHHHHH', data)
        
        return {
            'voice_id': voice_id,
            'enabled': bool(flags & 0x01),
            'priority': (flags >> 1) & 0x03,
            'cooldown_sec': cooldown,
            'param1': p1, 'param2': p2, 'param3': p3,
            'param4': p4, 'param5': p5
        }
```

### 7.2 JavaScript (微信小程序) 示例
```javascript
class CarVoiceBluetoothClient {
    parseConfigData(buffer) {
        const dataView = new DataView(buffer);
        let offset = 0;
        
        const configCount = dataView.getUint8(offset);
        offset += 4; // 跳过count和padding
        
        const configs = {};
        for (let i = 0; i < configCount; i++) {
            const config = {
                voiceId: dataView.getUint8(offset),
                enabled: (dataView.getUint8(offset + 1) & 0x01) !== 0,
                priority: (dataView.getUint8(offset + 1) >> 1) & 0x03,
                cooldownSec: dataView.getUint16(offset + 2, true),
                param1: dataView.getUint16(offset + 4, true),
                param2: dataView.getUint16(offset + 6, true),
                param3: dataView.getUint16(offset + 8, true),
                param4: dataView.getUint16(offset + 10, true),
                param5: dataView.getUint16(offset + 12, true)
            };
            configs[config.voiceId] = config;
            offset += 16;
        }
        
        return configs;
    }
}
```

## 8. 调试工具建议

### 8.1 协议分析器
- **十六进制查看器**: 显示原始数据包
- **结构化解析**: 自动解析协议字段
- **校验和验证**: 检查数据完整性

### 8.2 测试工具
- **批量测试**: 测试所有语音功能
- **压力测试**: 连续发送命令测试稳定性
- **错误注入**: 模拟各种错误情况

### 8.3 日志记录
- **通信日志**: 记录所有收发数据
- **错误日志**: 记录错误和异常
- **性能日志**: 记录响应时间和吞吐量

## 9. 数据包格式详解

### 9.1 获取所有配置响应数据包
```
字节偏移 | 字段名称 | 长度 | 描述
---------|----------|------|------
0        | 配置数量 | 1    | 当前配置的数量 (17)
1-3      | 填充     | 3    | 对齐填充，值为0
4-19     | 配置1    | 16   | 语音ID=1的配置数据
20-35    | 配置2    | 16   | 语音ID=2的配置数据
...      | ...      | ...  | ...
260-275  | 配置17   | 16   | 语音ID=17的配置数据
```

### 9.2 单个配置数据格式 (16字节)
```
字节偏移 | 字段名称     | 长度 | 描述
---------|-------------|------|------
0        | voice_id    | 1    | 语音ID (1-17)
1        | flags       | 1    | bit0:启用, bit1-2:优先级
2-3      | cooldown_ms | 2    | 冷却时间(秒)
4-5      | param1      | 2    | 参数1
6-7      | param2      | 2    | 参数2
8-9      | param3      | 2    | 参数3
10-11    | param4      | 2    | 参数4
12-13    | param5      | 2    | 参数5
14-15    | reserved    | 2    | 保留字段
```

### 9.3 参数含义对照表

| 语音ID | 功能名称 | param1 | param2 | param3 | param4 | param5 |
|--------|----------|--------|--------|--------|--------|--------|
| 1 | ACC开启欢迎语 | 延迟时间(秒) | - | - | - | - |
| 2-7 | 档位/车门提醒 | - | - | - | - | - |
| 8-11 | 车门未关警告 | 车速阈值(km/h) | - | - | - | - |
| 12 | 停车未熄火提醒 | 停车时间(分钟) | 提醒间隔(分钟) | 最大次数 | - | - |
| 13 | 方向盘预警 | 车速阈值(km/h) | 转角阈值(度) | 持续时间(秒) | - | - |
| 14 | 疲劳驾驶提醒 | 车速阈值(km/h) | 驾驶时间(小时) | - | - | - |
| 15 | 熄火物品提醒 | - | - | - | - | - |
| 16 | 方向盘未回正 | 转角阈值(度) | - | - | - | - |
| 17 | 转向灯持续过长 | 车速阈值(km/h) | 持续时间(秒) | 提醒间隔(秒) | 最大次数 | - |

## 10. 集成指南

### 10.1 ESP32端集成步骤

1. **添加源文件到CMakeLists.txt**:
```cmake
set(SOURCES
    # 现有文件...
    "car_voice_trigger/car_voice_config_protocol.cc"
    "car_voice_trigger/car_voice_config_manager.cc"
    "car_voice_trigger/car_voice_protocol_handler.cc"
    "car_voice_trigger/car_voice_uart_commands.cc"
    "car_voice_trigger/car_voice_config_system.cc"
)
```

2. **在main.cpp中初始化**:
```cpp
#include "car_voice_trigger/car_voice_config_system.h"

void app_main() {
    // 现有初始化代码...

    // 初始化汽车语音配置系统
    esp_err_t err = car_voice_config_system_init();
    if (err != ESP_OK) {
        ESP_LOGE("MAIN", "Failed to init car voice config system");
    }

    // 设置UART响应回调
    car_voice_config_system_set_uart_callback(uart_send_response);
}
```

3. **在UART命令处理中添加**:
```cpp
// 在现有的UART命令处理函数中添加
if (strncmp(command, "GET_CAR_VOICE_CONFIG", 20) == 0 ||
    strncmp(command, "SET_CAR_VOICE_CONFIG", 20) == 0 ||
    strncmp(command, "RESET_CAR_VOICE_CONFIG", 22) == 0 ||
    strncmp(command, "TEST_CAR_VOICE", 14) == 0 ||
    strncmp(command, "GET_CAR_VOICE_STATUS", 20) == 0) {

    car_voice_config_system_handle_uart_command(command, data);
    return;
}
```

4. **在语音触发系统中使用配置**:
```cpp
// 在car_voice_trigger.cc中使用配置
bool should_play_voice(uint8_t voice_id) {
    // 检查是否启用
    if (!car_voice_config_system_is_enabled(voice_id)) {
        return false;
    }

    // 检查冷却时间
    uint32_t cooldown = car_voice_config_system_get_cooldown(voice_id);
    if (get_time_since_last_play(voice_id) < cooldown) {
        return false;
    }

    // 获取优先级
    uint8_t priority = car_voice_config_system_get_priority(voice_id);

    return true;
}
```

### 10.2 电脑端开发建议

1. **使用现有串口通信框架**
2. **实现配置数据的本地缓存**
3. **添加配置文件导入/导出功能**
4. **实现实时配置同步**

## 11. 测试验证

### 11.1 功能测试清单
- [ ] 获取所有配置
- [ ] 设置所有配置
- [ ] 获取单个配置
- [ ] 设置单个配置
- [ ] 重置为默认配置
- [ ] 测试语音播放
- [ ] 获取系统状态
- [ ] 配置持久化存储
- [ ] 错误处理机制
- [ ] 数据包校验

### 11.2 性能测试
- **响应时间**: < 500ms
- **数据传输**: 276字节 < 1秒
- **内存使用**: < 2KB RAM
- **存储空间**: < 1KB NVS

### 11.3 兼容性测试
- **串口通信**: 115200 baud
- **蓝牙传输**: BLE MTU 512字节
- **协议版本**: 向后兼容

---

**文档版本**: v1.0
**最后更新**: 2025-08-04
**协议版本**: 0x01
**兼容性**: ESP32-S3, 串口通信, 蓝牙BLE

## 附录A: 默认配置数据

```
配置数量: 17 (0x11)
填充: 00 00 00

配置1 (ACC开启欢迎语):
01 01 00 1E 00 03 00 00 00 00 00 00 00 00 00 00

配置2 (R档倒车提醒):
02 03 00 05 00 00 00 00 00 00 00 00 00 00 00 00

... (其他配置)

完整十六进制数据:
11000000010001001E000300000000000000000000000000020003000500000000000000000000000000000003000100050000000000000000000000000000000004000100030000000000000000000000000000000005000100030000000000000000000000000000000006000100030000000000000000000000000000000007000100030000000000000000000000000000000008000300010000000A000000000000000000000000000009000300010000000A00000000000000000000000000000A000300010000000A00000000000000000000000000000B000300010000000A00000000000000000000000000000C0001000E10003C003C000300000000000000000000000D000300301E003C000F001400000000000000000000000E00030048001E000200000000000000000000000000000F0001000A000000000000000000000000000000000010000100050000000A000000000000000000000000001100010014001E001400000A000300000000000000000000
```
