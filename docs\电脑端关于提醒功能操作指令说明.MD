# 电脑端关于提醒功能操作指令说明

## 📖 概述

本文档详细说明了通过UART2串口与ESP32设备进行提醒功能操作的完整指令协议。适用于电脑端程序开发，支持创建、修改、删除、查询等全方位提醒管理功能。

## 🔌 连接参数

- **串口**: UART2
- **波特率**: 19200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控制**: 无
- **指令格式**: 直接发送指令，无需命令提示符

## 📋 指令分类

### 1. 查看操作指令

#### 1.1 查看所有提醒
```
REMINDER_LIST
```
**功能**: 显示所有活跃提醒的列表
**返回格式**:
```
OK:reminder_id1=标题1|2025-07-31 15:30:00|单次提醒,reminder_id2=标题2|09:00:00|每日提醒
```
**无提醒时返回**:
```
OK:no reminders
```

#### 1.2 查看指定提醒详情
```
REMINDER_GET <提醒ID>
```
**参数**:
- `<提醒ID>`: 提醒的唯一标识符（字符串）

**示例**:
```
REMINDER_GET reminder_1753967189_8549b0147c1e300c
```
**返回格式**:
```
OK:标题|2025-07-31 15:30:00|单次提醒
```
**提醒不存在时返回**:
```
ERROR:reminder not found
```

### 2. 创建提醒指令

#### 2.1 创建提醒（通用）
```
REMINDER_ADD "提醒标题" "时间" "类型"
```
**参数**:
- `"提醒标题"`: 提醒的标题/内容（必须用双引号包围）
- `"时间"`: 触发时间，格式为 `YYYY-MM-DD HH:MM:SS`
- `"类型"`: 提醒类型（可选，默认为once）

**支持的提醒类型**:
- `once`: 单次提醒
- `daily`: 每日提醒
- `weekly`: 每周提醒
- `monthly`: 每月提醒
- `yearly`: 每年提醒
- `countdown`: 倒计时提醒

**示例**:
```
REMINDER_ADD "开会" "2025-07-31 15:30:00" "once"
REMINDER_ADD "吃药" "2025-07-31 09:00:00" "daily"
REMINDER_ADD "周会" "2025-08-04 10:00:00" "weekly"
```

**成功返回**:
```
OK:added:reminder_1753967189_8549b0147c1e300c
```

**失败返回**:
```
ERROR:invalid time format
ERROR:add failed
```

### 3. 修改提醒指令

#### 3.1 修改提醒
```
REMINDER_MOD <提醒ID> "新标题" "新时间" "新类型"
```
**参数**:
- `<提醒ID>`: 要修改的提醒ID
- `"新标题"`: 新的提醒标题
- `"新时间"`: 新的触发时间，格式为 `YYYY-MM-DD HH:MM:SS`
- `"新类型"`: 新的提醒类型（可选，默认为once）

**示例**:
```
REMINDER_MOD reminder_1753967189_8549b0147c1e300c "重要会议" "2025-07-31 16:00:00" "once"
REMINDER_MOD reminder_1753967189_8549b0147c1e300c "每日吃药" "2025-07-31 09:00:00" "daily"
```

**成功返回**:
```
OK:modified
```

**失败返回**:
```
ERROR:reminder not found
ERROR:invalid time format
ERROR:modify failed
```

### 4. 删除操作指令

#### 4.1 删除指定提醒
```
REMINDER_DEL <提醒ID>
```
**参数**:
- `<提醒ID>`: 要删除的提醒ID

**示例**:
```
REMINDER_DEL reminder_1753967189_8549b0147c1e300c
```

**成功返回**:
```
OK:deleted
```

**失败返回**:
```
ERROR:reminder not found
```

#### 4.2 清理已完成提醒
```
REMINDER_CLEAR
```
**功能**: 清理所有已完成的提醒，释放存储空间

**返回格式**:
```
OK:cleared:3
```
其中数字3表示清理了3个已完成的提醒

## 📊 返回数据格式

### 成功响应格式
所有成功的操作都以 `OK:` 开头，后跟具体的响应数据：
```
OK:added:reminder_id
OK:modified
OK:deleted
OK:cleared:3
OK:no reminders
OK:标题|时间|类型
```

### 错误响应格式
所有错误都以 `ERROR:` 开头，后跟错误描述：
```
ERROR:command not found
ERROR:invalid time format
ERROR:reminder not found
ERROR:add failed
ERROR:modify failed
```

## ⚠️ 重要注意事项

### 1. 提醒ID格式
- 提醒ID是系统自动生成的唯一标识符
- 格式：`reminder_时间戳_随机数`
- 示例：`reminder_1753967189_8549b0147c1e300c`
- 在操作提醒时必须使用完整的ID

### 2. 时间格式要求
- **必须使用格式**：`YYYY-MM-DD HH:MM:SS`
- **正确示例**：`2025-07-31 15:30:00`
- **错误示例**：`2025/7/31 3:30PM`、`31-07-2025 15:30`

### 3. 字符串参数
- 包含空格的参数必须用双引号包围
- 示例：`"重要会议"`、`"2025-07-31 15:30:00"`
- 特殊字符会被自动处理，无需手动转义

## 🔧 开发注意事项

### 1. 串口通信
- **波特率**: 19200（注意不是115200）
- **串口**: UART2
- 发送指令后等待响应，通常在1-3秒内返回
- 每行指令以换行符`\n`结束
- 支持UTF-8编码，可以使用中文内容

### 2. 指令格式
- 所有指令都是大写字母加下划线格式
- 参数用空格分隔
- 字符串参数必须用双引号包围
- 不需要命令提示符，直接发送指令

### 3. 提醒识别日志
当系统识别到有效的提醒请求时，会在日志中显示格式化的提醒信息：
```
---------------------------------------------------------
🎯 提醒设定
📝 提醒内容: 带手机
⏰ 提醒时间: 2025-07-31 13:30:00
🔄 提醒周期: 单次提醒
🎵 语音文本: 带手机
📊 置信度: 0.85
🆔 任务ID: reminder_1753967189_8549b0147c1e300c
---------------------------------------------------------
```

### 4. 存储空间
- NVS分区大小：64KB
- 可存储大约128-320个提醒任务
- 系统会自动清理已完成的提醒
- 存储空间不足时会自动清理旧数据

## 📝 使用示例

### 完整操作流程示例
```
# 1. 查看当前所有提醒
REMINDER_LIST

# 2. 创建几个不同类型的提醒
REMINDER_ADD "重要会议" "2025-07-31 15:30:00" "once"
REMINDER_ADD "每日吃药" "2025-07-31 09:00:00" "daily"
REMINDER_ADD "周会" "2025-08-04 10:00:00" "weekly"

# 3. 再次查看提醒列表，确认创建成功
REMINDER_LIST

# 4. 查看某个具体提醒的详情
REMINDER_GET reminder_1753967189_8549b0147c1e300c

# 5. 修改某个提醒
REMINDER_MOD reminder_1753967189_8549b0147c1e300c "非常重要的会议" "2025-07-31 16:00:00" "once"

# 6. 删除不需要的提醒
REMINDER_DEL reminder_1753967189_8549b0147c1e300c

# 7. 清理已完成的提醒
REMINDER_CLEAR
```

### 实际测试示例
以下是实际测试中的指令和响应：

**创建提醒**:
```
发送: REMINDER_ADD "带手机" "2025-07-31 13:30:00" "once"
响应: OK:added:reminder_1753967189_8549b0147c1e300c
```

**查看提醒列表**:
```
发送: REMINDER_LIST
响应: OK:reminder_1753967189_8549b0147c1e300c=带手机|2025-07-31 13:30:00|单次提醒
```

**查看提醒详情**:
```
发送: REMINDER_GET reminder_1753967189_8549b0147c1e300c
响应: OK:带手机|2025-07-31 13:30:00|单次提醒
```

## 🚀 版本信息

- **文档版本**: 2.0.0
- **适用固件版本**: xiaozhi v1.7.5+
- **最后更新**: 2025-07-31
- **兼容性**: ESP32-S3平台
- **UART协议版本**: 2.0

---

**注意**: 本文档基于最新的UART命令系统编写，与之前的版本不兼容。请确保使用正确的指令格式进行开发和测试。
