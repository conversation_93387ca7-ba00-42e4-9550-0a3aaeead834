#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
实现应用程序的主界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog, filedialog
import threading
import logging
import json
import os
from datetime import datetime
from typing import Optional, Dict, Any

from core.serial_comm import SerialCommunication
from core.command_manager import CommandManager
from core.config_manager import ConfigManager
from core.log_manager import LogManager, GUILogHandler
from core.uart_info_manager import UARTInfoManager
from gui.dialogs.command_dialog import CommandDialog
from gui.dialogs.settings_dialog import SettingsDialog
from gui.tabs.car_voice_tab import CarVoiceTab
from utils.validators import Validators

class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.log_manager = LogManager(
            log_file=self.config_manager.get_log_file_path(),
            level=self.config_manager.get_log_level()
        )
        self.gui_log_handler = GUILogHandler(self.log_manager)
        
        self.serial_comm = SerialCommunication()
        self.command_manager = CommandManager(self.serial_comm)
        self.uart_info_manager = UARTInfoManager(self.serial_comm)
        
        # 界面变量
        self.port_var = tk.StringVar()
        self.baudrate_var = tk.StringVar(value="19200")  # 默认19200（指令模式）
        self.status_var = tk.StringVar(value="未连接")
        self.text_count_var = tk.StringVar(value="0/50")
        self.system_count_var = tk.StringVar(value="0")
        self.memory_var = tk.StringVar(value="--KB")
        self.reminder_count_var = tk.StringVar(value="0")
        self.car_voice_status_var = tk.StringVar(value="未同步")
        
        # 控件引用
        self.cmd_tree = None
        self.log_text = None
        self.connect_btn = None
        self.disconnect_btn = None
        self.port_combo = None
        self.car_voice_tab = None
        
        # 定时器
        self.refresh_timer = None
        self.log_update_timer = None
        self.keepalive_timer = None
        self.blink_timer = None

        # 状态显示相关
        self.status_label_widget = None
        self.blink_state = False

        # 初始化提醒指令数据
        self.init_reminder_commands()

        self.setup_window()
        self.setup_ui()
        self.load_settings()
        self.start_timers()

        self.logger.info("主窗口初始化完成")

        # 启动时初始化基本数据（不依赖设备连接）
        self.root.after(500, self.initialize_basic_data)

    def init_reminder_commands(self):
        """初始化提醒管理数据结构"""
        # 提醒数据存储
        self.reminder_data = {}



    def setup_window(self):
        """设置窗口属性"""
        self.root.title("小智ESP32指令管理器 v1.0")
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("resources/icon.ico")
            pass
        except:
            pass
        
        # 加载窗口几何信息
        width, height, x, y = self.config_manager.get_window_geometry()
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 设置最小尺寸
        self.root.minsize(800, 600)
        
        # 绑定窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.bind("<Configure>", self.on_window_configure)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建菜单栏
        self.create_menu()
        
        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建上部区域（垂直排列：串口连接、设备信息、系统状态）
        top_frame = ttk.Frame(main_frame)
        top_frame.pack(fill=tk.X, pady=(0, 5))

        # 第一层：串口连接区域
        self.create_connection_area(top_frame)

        # 第二层：设备信息区域（四个方框排成一行）
        self.create_device_info_area(top_frame)

        # 第三层：系统状态区域
        self.create_basic_status_area(top_frame)
        
        # 创建中部区域（指令管理区）- 固定高度
        middle_frame = ttk.LabelFrame(main_frame, text="指令管理")
        middle_frame.pack(fill=tk.X, expand=False, pady=(0, 5))

        self.create_command_area(middle_frame)

        # 创建下部区域（日志输出区）- 可扩展高度
        bottom_frame = ttk.LabelFrame(main_frame, text="日志输出")
        bottom_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        self.create_log_area(bottom_frame)
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入指令...", command=self.import_commands)
        file_menu.add_command(label="导出指令...", command=self.export_commands)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.on_closing)
        
        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="添加指令", command=self.add_command)
        edit_menu.add_command(label="编辑指令", command=self.edit_command)
        edit_menu.add_command(label="删除指令", command=self.delete_command)
        edit_menu.add_separator()
        edit_menu.add_command(label="刷新列表", command=self.refresh_commands)
        
        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="设置...", command=self.show_settings)
        tools_menu.add_command(label="清空日志", command=self.clear_logs)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    

    def create_connection_area(self, parent):
        """创建串口连接区域"""
        conn_frame = ttk.LabelFrame(parent, text="串口连接")
        conn_frame.pack(fill=tk.X, pady=(0, 5))

        # 所有组件在一行：波特率、端口、按钮组、刷新串口
        params_frame = ttk.Frame(conn_frame)
        params_frame.pack(fill=tk.X, padx=5, pady=5)

        # 波特率选择（最左侧）
        ttk.Label(params_frame, text="波特率:").pack(side=tk.LEFT)
        self.baudrate_combo = ttk.Combobox(params_frame, textvariable=self.baudrate_var,
                                          values=["19200", "115200", "9600", "38400", "57600"],
                                          width=8, state="readonly")
        self.baudrate_combo.pack(side=tk.LEFT, padx=(5, 15))

        # 端口选择
        ttk.Label(params_frame, text="端口:").pack(side=tk.LEFT)
        self.port_combo = ttk.Combobox(params_frame, textvariable=self.port_var, width=10, state="readonly")
        self.port_combo.pack(side=tk.LEFT, padx=(5, 15))

        # 按钮组（连接、断开、PING）
        self.connect_btn = ttk.Button(params_frame, text="连接", command=self.connect_device)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 2))

        self.disconnect_btn = ttk.Button(params_frame, text="断开", command=self.disconnect_device, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 2))

        self.ping_btn = ttk.Button(params_frame, text="PING", command=self.ping_test, state=tk.DISABLED)
        self.ping_btn.pack(side=tk.LEFT, padx=(0, 15))

        # 刷新串口按钮（最右侧）
        ttk.Button(params_frame, text="刷新串口", command=self.refresh_ports).pack(side=tk.RIGHT)

        # 初始化端口列表
        self.refresh_ports()
    
    def create_basic_status_area(self, parent):
        """创建基本系统状态区域（横向布局，与连接区域同宽）"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态")
        status_frame.pack(fill=tk.X, pady=(5, 0))

        # 横向布局的状态信息
        info_frame = ttk.Frame(status_frame)
        info_frame.pack(fill=tk.X, padx=5, pady=5)

        # 全部刷新按钮（最左侧）
        ttk.Button(info_frame, text="全部刷新", command=self.refresh_all_data).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))

        # 连接状态
        ttk.Label(info_frame, text="连接状态:").grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        self.status_label_widget = tk.Label(info_frame, textvariable=self.status_var, fg='green', bg='white')
        self.status_label_widget.grid(row=0, column=2, sticky=tk.W, padx=(0, 20))

        # 文本指令
        ttk.Label(info_frame, text="文本指令:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.text_count_label = tk.Label(info_frame, textvariable=self.text_count_var, fg='green', bg='white')
        self.text_count_label.grid(row=0, column=4, sticky=tk.W, padx=(0, 20))

        # 系统指令
        ttk.Label(info_frame, text="系统指令:").grid(row=0, column=5, sticky=tk.W, padx=(0, 5))
        self.system_count_label = tk.Label(info_frame, textvariable=self.system_count_var, fg='green', bg='white')
        self.system_count_label.grid(row=0, column=6, sticky=tk.W, padx=(0, 20))

        # 提醒列表
        ttk.Label(info_frame, text="提醒列表:").grid(row=0, column=7, sticky=tk.W, padx=(0, 5))
        self.reminder_count_var = tk.StringVar(value="0")
        self.reminder_count_label = tk.Label(info_frame, textvariable=self.reminder_count_var, fg='green', bg='white')
        self.reminder_count_label.grid(row=0, column=8, sticky=tk.W, padx=(0, 20))

        # 汽车语音状态
        ttk.Label(info_frame, text="汽车语音:").grid(row=0, column=9, sticky=tk.W, padx=(0, 5))
        self.car_voice_status_var = tk.StringVar(value="未同步")
        self.car_voice_status_label = tk.Label(info_frame, textvariable=self.car_voice_status_var, fg='orange', bg='white')
        self.car_voice_status_label.grid(row=0, column=10, sticky=tk.W)

        # 配置网格权重
        info_frame.columnconfigure(10, weight=1)

    def create_device_info_area(self, parent):
        """创建设备信息显示区域 - 四个方框排成一行"""
        device_frame = ttk.LabelFrame(parent, text="🔍 设备信息")
        device_frame.pack(fill=tk.X, pady=(0, 5))

        # 控制面板
        control_frame = ttk.Frame(device_frame)
        control_frame.pack(fill=tk.X, padx=5, pady=(5, 2))

        # 手动刷新按钮
        ttk.Button(control_frame, text="🔄", width=3,
                  command=self.manual_refresh_device_info).pack(side=tk.LEFT, padx=(0, 5))

        # 定时查询控制
        ttk.Label(control_frame, text="⏰").pack(side=tk.LEFT)
        self.auto_interval_var = tk.StringVar(value="5秒")
        interval_combo = ttk.Combobox(control_frame, textvariable=self.auto_interval_var,
                                     values=["3秒", "5秒", "10秒", "30秒"], width=6, state="readonly")
        interval_combo.pack(side=tk.LEFT, padx=(2, 5))
        interval_combo.bind('<<ComboboxSelected>>', self.on_interval_changed)

        # 启动/停止按钮
        self.auto_query_btn = ttk.Button(control_frame, text="▶️", width=3,
                                        command=self.toggle_auto_query)
        self.auto_query_btn.pack(side=tk.LEFT)

        # 设备信息显示区域 - 四个方框排成一行
        info_container = ttk.Frame(device_frame)
        info_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=(2, 5))

        # 硬件信息方框
        hw_frame = ttk.LabelFrame(info_container, text="📱 硬件")
        hw_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        hw_info = ttk.Frame(hw_frame)
        hw_info.pack(fill=tk.BOTH, expand=True, padx=3, pady=2)

        ttk.Label(hw_info, text="芯片:", font=('', 8)).grid(row=0, column=0, sticky=tk.W)
        self.chip_label = tk.Label(hw_info, text="--", font=('', 8), fg='green', bg='white')
        self.chip_label.grid(row=0, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(hw_info, text="版本:", font=('', 8)).grid(row=1, column=0, sticky=tk.W)
        self.version_label = tk.Label(hw_info, text="--", font=('', 8), fg='green', bg='white')
        self.version_label.grid(row=1, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(hw_info, text="MAC:", font=('', 8)).grid(row=2, column=0, sticky=tk.W)
        self.mac_label = tk.Label(hw_info, text="--", font=('', 8), fg='green', bg='white')
        self.mac_label.grid(row=2, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(hw_info, text="闪存:", font=('', 8)).grid(row=3, column=0, sticky=tk.W)
        self.flash_label = tk.Label(hw_info, text="--", font=('', 8), fg='green', bg='white')
        self.flash_label.grid(row=3, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(hw_info, text="PSRAM:", font=('', 8)).grid(row=4, column=0, sticky=tk.W)
        self.psram_label = tk.Label(hw_info, text="--", font=('', 8), fg='green', bg='white')
        self.psram_label.grid(row=4, column=1, sticky=tk.W, padx=(2, 0))

        # 音频信息方框
        audio_frame = ttk.LabelFrame(info_container, text="🔊 音频")
        audio_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        audio_info = ttk.Frame(audio_frame)
        audio_info.pack(fill=tk.BOTH, expand=True, padx=3, pady=2)

        ttk.Label(audio_info, text="音量:", font=('', 8)).grid(row=0, column=0, sticky=tk.W)
        self.volume_label = tk.Label(audio_info, text="--", font=('', 8), fg='green', bg='white')
        self.volume_label.grid(row=0, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(audio_info, text="编解码:", font=('', 8)).grid(row=1, column=0, sticky=tk.W)
        self.codec_label = tk.Label(audio_info, text="--", font=('', 8), fg='green', bg='white')
        self.codec_label.grid(row=1, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(audio_info, text="麦克风:", font=('', 8)).grid(row=2, column=0, sticky=tk.W)
        self.mic_label = tk.Label(audio_info, text="--", font=('', 8), fg='green', bg='white')
        self.mic_label.grid(row=2, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(audio_info, text="扬声器:", font=('', 8)).grid(row=3, column=0, sticky=tk.W)
        self.speaker_label = tk.Label(audio_info, text="--", font=('', 8), fg='green', bg='white')
        self.speaker_label.grid(row=3, column=1, sticky=tk.W, padx=(2, 0))

        # 网络信息方框
        net_frame = ttk.LabelFrame(info_container, text="🌐 网络")
        net_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 2))

        # 创建网络信息内容
        self.create_network_info_content(net_frame)

        # 系统信息方框
        sys_frame = ttk.LabelFrame(info_container, text="💾 系统")
        sys_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        sys_info = ttk.Frame(sys_frame)
        sys_info.pack(fill=tk.BOTH, expand=True, padx=3, pady=2)

        ttk.Label(sys_info, text="运行时间:", font=('', 8)).grid(row=0, column=0, sticky=tk.W)
        self.uptime_label = tk.Label(sys_info, text="--", font=('', 8), fg='green', bg='white')
        self.uptime_label.grid(row=0, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(sys_info, text="SRAM:", font=('', 8)).grid(row=1, column=0, sticky=tk.W)
        self.sram_label = tk.Label(sys_info, text="--", font=('', 8), fg='green', bg='white')
        self.sram_label.grid(row=1, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(sys_info, text="PSRAM:", font=('', 8)).grid(row=2, column=0, sticky=tk.W)
        self.psram_sys_label = tk.Label(sys_info, text="--", font=('', 8), fg='green', bg='white')
        self.psram_sys_label.grid(row=2, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(sys_info, text="Flash:", font=('', 8)).grid(row=3, column=0, sticky=tk.W)
        self.flash_sys_label = tk.Label(sys_info, text="--", font=('', 8), fg='green', bg='white')
        self.flash_sys_label.grid(row=3, column=1, sticky=tk.W, padx=(2, 0))

        ttk.Label(sys_info, text="任务数:", font=('', 8)).grid(row=4, column=0, sticky=tk.W)
        self.tasks_label = tk.Label(sys_info, text="--", font=('', 8), fg='green', bg='white')
        self.tasks_label.grid(row=4, column=1, sticky=tk.W, padx=(2, 0))

        # 配置网格权重
        hw_info.columnconfigure(1, weight=1)
        audio_info.columnconfigure(1, weight=1)
        sys_info.columnconfigure(1, weight=1)

    def create_network_info_content(self, parent):
        """创建网络信息内容"""
        self.net_info = ttk.Frame(parent)
        self.net_info.pack(fill=tk.BOTH, expand=True, padx=3, pady=2)

        # 创建WiFi标签（初始隐藏）- 使用tk.Label支持颜色
        self.wifi_labels = {}
        self.wifi_labels['type'] = ttk.Label(self.net_info, text="WiFi:", font=('', 8))
        self.wifi_labels['ssid'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')
        self.wifi_labels['signal_text'] = ttk.Label(self.net_info, text="信号:", font=('', 8))
        self.wifi_labels['signal'] = tk.Label(self.net_info, text="📶 --", font=('', 8), fg='green', bg='white')
        self.wifi_labels['ip_text'] = ttk.Label(self.net_info, text="IP:", font=('', 8))
        self.wifi_labels['ip'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')
        self.wifi_labels['channel_text'] = ttk.Label(self.net_info, text="信道:", font=('', 8))
        self.wifi_labels['channel'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')

        # 创建4G标签（初始隐藏）- 使用tk.Label支持颜色
        self.g4_labels = {}
        self.g4_labels['type'] = ttk.Label(self.net_info, text="4G:", font=('', 8))
        self.g4_labels['operator'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')
        self.g4_labels['signal_text'] = ttk.Label(self.net_info, text="信号:", font=('', 8))
        self.g4_labels['signal'] = tk.Label(self.net_info, text="📶 --", font=('', 8), fg='green', bg='white')
        self.g4_labels['ip_text'] = ttk.Label(self.net_info, text="IP:", font=('', 8))
        self.g4_labels['ip'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')
        self.g4_labels['iccid_text'] = ttk.Label(self.net_info, text="ICCID:", font=('', 8))
        self.g4_labels['iccid'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')
        self.g4_labels['imei_text'] = ttk.Label(self.net_info, text="IMEI:", font=('', 8))
        self.g4_labels['imei'] = tk.Label(self.net_info, text="--", font=('', 8), fg='green', bg='white')

        # 初始状态：隐藏所有网络标签
        self.current_network_type = None

    def show_wifi_info(self):
        """显示WiFi信息，隐藏4G信息"""
        # 隐藏4G标签
        if self.current_network_type == '4g':
            for label in self.g4_labels.values():
                label.grid_remove()

        # 显示WiFi标签
        self.wifi_labels['type'].grid(row=0, column=0, sticky=tk.W)
        self.wifi_labels['ssid'].grid(row=0, column=1, sticky=tk.W, padx=(2, 0))
        self.wifi_labels['signal_text'].grid(row=1, column=0, sticky=tk.W)
        self.wifi_labels['signal'].grid(row=1, column=1, sticky=tk.W, padx=(2, 0))
        self.wifi_labels['ip_text'].grid(row=2, column=0, sticky=tk.W)
        self.wifi_labels['ip'].grid(row=2, column=1, sticky=tk.W, padx=(2, 0))
        self.wifi_labels['channel_text'].grid(row=3, column=0, sticky=tk.W)
        self.wifi_labels['channel'].grid(row=3, column=1, sticky=tk.W, padx=(2, 0))

        self.current_network_type = 'wifi'

    def show_4g_info(self):
        """显示4G信息，隐藏WiFi信息"""
        # 隐藏WiFi标签
        if self.current_network_type == 'wifi':
            for label in self.wifi_labels.values():
                label.grid_remove()

        # 显示4G标签
        self.g4_labels['type'].grid(row=0, column=0, sticky=tk.W)
        self.g4_labels['operator'].grid(row=0, column=1, sticky=tk.W, padx=(2, 0))
        self.g4_labels['signal_text'].grid(row=1, column=0, sticky=tk.W)
        self.g4_labels['signal'].grid(row=1, column=1, sticky=tk.W, padx=(2, 0))
        self.g4_labels['ip_text'].grid(row=2, column=0, sticky=tk.W)
        self.g4_labels['ip'].grid(row=2, column=1, sticky=tk.W, padx=(2, 0))
        self.g4_labels['iccid_text'].grid(row=3, column=0, sticky=tk.W)
        self.g4_labels['iccid'].grid(row=3, column=1, sticky=tk.W, padx=(2, 0))
        self.g4_labels['imei_text'].grid(row=4, column=0, sticky=tk.W)
        self.g4_labels['imei'].grid(row=4, column=1, sticky=tk.W, padx=(2, 0))

        self.current_network_type = '4g'

    def hide_all_network_info(self):
        """隐藏所有网络信息"""
        try:
            # 隐藏WiFi标签
            if hasattr(self, 'wifi_labels'):
                for label in self.wifi_labels.values():
                    label.grid_remove()

            # 隐藏4G标签
            if hasattr(self, 'g4_labels'):
                for label in self.g4_labels.values():
                    label.grid_remove()

            self.current_network_type = None
            self.logger.debug("所有网络信息已隐藏")

        except Exception as e:
            self.logger.error(f"隐藏网络信息失败: {e}")

    def get_operator_chinese_name(self, operator):
        """将运营商英文名转换为中文名"""
        operator_map = {
            'CHINA MOBILE': '中国移动',
            'CHINA UNICOM': '中国联通',
            'CHINA TELECOM': '中国电信',
            'CMCC': '中国移动',
            'CU': '中国联通',
            'CT': '中国电信'
        }
        return operator_map.get(operator.upper(), operator)

    def get_4g_signal_icon(self, signal_strength):
        """根据4G信号强度返回图标"""
        if signal_strength == 'strong':
            return '📶'
        elif signal_strength == 'medium':
            return '📶'
        elif signal_strength == 'weak':
            return '📶'
        else:
            return '📵'

    def manual_refresh_device_info(self):
        """手动刷新设备信息"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showwarning("提示", "请先连接设备")
                return

            self.logger.info("手动刷新设备信息...")

            def refresh_thread():
                try:
                    info = self.uart_info_manager.query_system_info()
                    if info:
                        self.root.after(0, self.update_device_info_display, info)
                        self.root.after(0, lambda: self.logger.info("设备信息刷新成功"))
                    else:
                        self.root.after(0, self.clear_device_info_display)
                        self.root.after(0, lambda: messagebox.showerror("错误", "设备信息查询失败，显示已清空"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"刷新失败: {e}"))

            threading.Thread(target=refresh_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"手动刷新设备信息失败: {e}")
            messagebox.showerror("错误", f"刷新失败: {e}")

    def toggle_auto_query(self):
        """切换自动查询状态"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showwarning("提示", "请先连接设备")
                return

            if not hasattr(self, '_auto_query_running') or not self._auto_query_running:
                # 启动自动查询
                interval_text = self.auto_interval_var.get()
                interval = int(interval_text.replace('秒', ''))

                self.uart_info_manager.start_auto_query(
                    callback=self.on_auto_query_result,
                    interval=interval
                )

                self._auto_query_running = True
                self.auto_query_btn.config(text="⏸️")
                self.logger.info(f"自动查询已启动，间隔: {interval}秒")
            else:
                # 停止自动查询
                self.uart_info_manager.stop_auto_query()
                self._auto_query_running = False
                self.auto_query_btn.config(text="▶️")
                self.logger.info("自动查询已停止")

        except Exception as e:
            self.logger.error(f"切换自动查询状态失败: {e}")
            messagebox.showerror("错误", f"操作失败: {e}")

    def on_interval_changed(self, event=None):
        """查询间隔改变时的处理"""
        try:
            if hasattr(self, '_auto_query_running') and self._auto_query_running:
                # 如果正在运行，重启自动查询以应用新间隔
                self.uart_info_manager.stop_auto_query()

                interval_text = self.auto_interval_var.get()
                interval = int(interval_text.replace('秒', ''))

                self.uart_info_manager.start_auto_query(
                    callback=self.on_auto_query_result,
                    interval=interval
                )

                self.logger.info(f"自动查询间隔已更新为: {interval}秒")
        except Exception as e:
            self.logger.error(f"更新查询间隔失败: {e}")

    def on_auto_query_result(self, info):
        """自动查询结果回调"""
        try:
            if info:
                self.root.after(0, self.update_device_info_display, info)
            else:
                self.logger.warning("自动查询返回空数据，清空显示")
                self.root.after(0, self.clear_device_info_display)
        except Exception as e:
            self.logger.error(f"自动查询结果处理失败: {e}")

    def update_device_info_display(self, info):
        """更新设备信息显示"""
        try:
            if not info:
                # 清空所有显示，显示为空
                self.clear_device_info_display()
                return

            # 更新硬件信息
            if 'hardware' in info:
                try:
                    hw = info['hardware']
                    self.chip_label.config(text=hw.get('chip', '--'))
                    self.version_label.config(text=hw.get('version', '--'))
                    self.mac_label.config(text=hw.get('mac', '--'))
                    self.flash_label.config(text=hw.get('flash_size', '--'))
                    self.psram_label.config(text=hw.get('psram_size', '--'))
                except Exception as e:
                    self.logger.error(f"更新硬件信息失败: {e}")

            # 更新网络信息（智能显示WiFi或4G）
            if 'network' in info and hasattr(self, 'wifi_labels') and hasattr(self, 'g4_labels'):
                net = info['network']

                # 检查WiFi连接状态
                wifi_connected = False
                if 'wifi' in net:
                    wifi = net['wifi']
                    wifi_status = wifi.get('status', 'disconnected')
                    if wifi_status == 'connected':
                        wifi_connected = True

                # 检查4G连接状态
                g4_connected = False
                if '4g' in net:
                    g4 = net['4g']
                    g4_status = g4.get('status', 'disconnected')
                    if g4_status == 'connected':
                        g4_connected = True

                # 根据连接状态决定显示哪种网络
                if wifi_connected:
                    # 显示WiFi信息
                    self.show_wifi_info()
                    wifi = net['wifi']
                    ssid = wifi.get('ssid', '--')

                    if 'ssid' in self.wifi_labels:
                        self.wifi_labels['ssid'].config(text=ssid)

                    ip = wifi.get('ip', '--')
                    if 'ip' in self.wifi_labels:
                        self.wifi_labels['ip'].config(text=ip)

                    rssi = wifi.get('rssi', 0)
                    signal_icon = self.uart_info_manager.get_signal_icon(rssi)
                    if 'signal' in self.wifi_labels:
                        self.wifi_labels['signal'].config(text=f"{signal_icon} {rssi}dBm" if rssi != 0 else "📵 --")

                    channel = wifi.get('channel', 0)
                    if 'channel' in self.wifi_labels:
                        self.wifi_labels['channel'].config(text=str(channel) if channel != 0 else '--')

                elif g4_connected:
                    # 显示4G信息
                    self.show_4g_info()
                    g4 = net['4g']

                    # 运营商识别和显示
                    operator = g4.get('operator', '--')
                    operator_cn = self.get_operator_chinese_name(operator)
                    if 'operator' in self.g4_labels:
                        self.g4_labels['operator'].config(text=operator_cn)

                    # 4G IP
                    ip = g4.get('ip', '--')
                    if 'ip' in self.g4_labels:
                        self.g4_labels['ip'].config(text=ip)

                    # 信号强度
                    signal_strength = g4.get('signal_strength', 'unknown')
                    signal_icon = self.get_4g_signal_icon(signal_strength)
                    if 'signal' in self.g4_labels:
                        self.g4_labels['signal'].config(text=f"{signal_icon} {signal_strength}")

                    # ICCID - 完整显示
                    iccid = g4.get('iccid', '--')
                    if iccid and iccid != 'Unknown':
                        display_iccid = iccid
                    else:
                        display_iccid = '--'
                    if 'iccid' in self.g4_labels:
                        self.g4_labels['iccid'].config(text=display_iccid)

                    # IMEI - 完整显示
                    imei = g4.get('imei', '--')
                    if imei and imei != 'Unknown':
                        display_imei = imei
                    else:
                        display_imei = '--'
                    if 'imei' in self.g4_labels:
                        self.g4_labels['imei'].config(text=display_imei)

            # 更新音频信息
            if 'audio' in info:
                try:
                    audio = info['audio']
                    volume = audio.get('volume', 0)
                    self.logger.info(f"界面更新音频信息 - 音量: {volume}, 完整音频数据: {audio}")
                    self.volume_label.config(text=f"{volume}%" if volume != 0 else "--")
                    self.codec_label.config(text=audio.get('codec', '--'))

                    mic_status = "✅" if audio.get('mic_status') == 'active' else "❌"
                    speaker_status = "✅" if audio.get('speaker_status') == 'active' else "❌"
                    self.mic_label.config(text=mic_status)
                    self.speaker_label.config(text=speaker_status)
                except Exception as e:
                    self.logger.error(f"更新音频信息失败: {e}")
            else:
                self.logger.warning(f"设备信息中没有音频数据，可用键: {list(info.keys()) if info else 'None'}")

            # 更新系统信息
            if 'system' in info:
                try:
                    sys = info['system']
                    uptime = self.uart_info_manager.format_uptime(sys.get('uptime', 0))
                    self.uptime_label.config(text=uptime)

                    # 更新新的内存字段格式
                    sram = sys.get('sram', '--')
                    self.sram_label.config(text=sram)

                    psram = sys.get('psram', '--')
                    self.psram_label.config(text=psram)

                    flash = sys.get('flash', '--')
                    self.flash_label.config(text=flash)

                    tasks = sys.get('tasks', 0)
                    self.tasks_label.config(text=str(tasks) if tasks != 0 else "--")
                except Exception as e:
                    self.logger.error(f"更新系统信息失败: {e}")

        except Exception as e:
            self.logger.error(f"更新设备信息显示失败: {e}")

    def clear_device_info_display(self):
        """清空设备信息显示"""
        try:
            # 清空硬件信息
            self.chip_label.config(text="--")
            self.version_label.config(text="--")
            self.mac_label.config(text="--")
            self.flash_label.config(text="--")
            self.psram_label.config(text="--")

            # 清空音频信息
            self.volume_label.config(text="--")
            self.codec_label.config(text="--")
            self.mic_label.config(text="--")
            self.speaker_label.config(text="--")

            # 清空系统信息
            self.uptime_label.config(text="--")
            self.sram_label.config(text="--")
            self.psram_label.config(text="--")
            self.flash_label.config(text="--")
            self.tasks_label.config(text="--")

            # 清空网络信息
            self.hide_all_network_info()

            self.logger.info("设备信息显示已清空")

        except Exception as e:
            self.logger.error(f"清空设备信息显示失败: {e}")

    def start_device_info_auto_query(self):
        """启动设备信息自动查询"""
        try:
            if not self.serial_comm.is_port_connected():
                return

            # 首先进行一次手动查询
            self.logger.info("连接成功，开始查询设备信息...")

            def initial_query():
                try:
                    info = self.uart_info_manager.query_system_info()
                    if info:
                        self.root.after(0, self.update_device_info_display, info)
                        self.logger.info("设备信息初始查询成功")

                        # 启动自动查询
                        interval_text = self.auto_interval_var.get()
                        interval = int(interval_text.replace('秒', ''))

                        self.uart_info_manager.start_auto_query(
                            callback=self.on_auto_query_result,
                            interval=interval
                        )

                        self._auto_query_running = True
                        self.root.after(0, lambda: self.auto_query_btn.config(text="⏸️"))
                        self.logger.info(f"设备信息自动查询已启动，间隔: {interval}秒")
                    else:
                        self.logger.warning("设备信息初始查询失败，显示为空")
                        self.root.after(0, self.clear_device_info_display)
                except Exception as e:
                    self.logger.error(f"设备信息初始查询异常: {e}")

            threading.Thread(target=initial_query, daemon=True).start()

        except Exception as e:
            self.logger.error(f"启动设备信息自动查询失败: {e}")

    def stop_device_info_auto_query(self):
        """停止设备信息自动查询"""
        try:
            if hasattr(self, '_auto_query_running') and self._auto_query_running:
                self.uart_info_manager.stop_auto_query()
                self._auto_query_running = False
                self.auto_query_btn.config(text="▶️")
                self.logger.info("设备信息自动查询已停止")
        except Exception as e:
            self.logger.error(f"停止设备信息自动查询失败: {e}")

    def create_command_area(self, parent):
        """创建指令管理区域"""
        # 标签页 - 固定高度，不随窗口扩展
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.X, expand=False, padx=5, pady=5)

        # 配置标签页样式
        style = ttk.Style()

        # 文本指令标签页（绿色）
        text_frame = ttk.Frame(notebook)
        notebook.add(text_frame, text="📄 文本指令")

        # 指令列表 - 固定高度
        list_frame = ttk.Frame(text_frame)
        list_frame.pack(fill=tk.X, expand=False, pady=(0, 5))

        # 创建Treeview
        columns = ("名称", "文本内容")
        self.cmd_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)

        # 设置列标题和宽度
        self.cmd_tree.heading("名称", text="指令名称")
        self.cmd_tree.heading("文本内容", text="指令内容")

        self.cmd_tree.column("名称", width=150)
        self.cmd_tree.column("文本内容", width=400)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.cmd_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.cmd_tree.xview)
        self.cmd_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.cmd_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        list_frame.grid_rowconfigure(0, weight=1)
        list_frame.grid_columnconfigure(0, weight=1)

        # 绑定双击事件 - 双击执行指令
        self.cmd_tree.bind("<Double-1>", lambda event: self.execute_text_command())

        # 按钮区域
        btn_frame = ttk.Frame(text_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="添加指令", command=self.add_command).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="编辑指令", command=self.edit_command).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="删除指令", command=self.delete_command).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="执行指令", command=self.execute_command).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="全部重置", command=self.reset_commands).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="刷新列表", command=self.refresh_commands).pack(side=tk.LEFT, padx=(0, 5))

        # 系统指令标签页（红色）
        system_frame = ttk.Frame(notebook)
        notebook.add(system_frame, text="⚙️ 系统指令")

        # 提醒列表标签页（蓝色）
        reminder_frame = ttk.Frame(notebook)
        notebook.add(reminder_frame, text="🔔 提醒列表")

        # 汽车语音设置标签页
        self.car_voice_tab = CarVoiceTab(notebook, self.serial_comm)

        # 系统指令列表 - 固定高度
        system_list_frame = ttk.Frame(system_frame)
        system_list_frame.pack(fill=tk.X, expand=False, pady=(0, 5))

        system_columns = ("名称", "协议格式", "描述", "类型")
        self.system_tree = ttk.Treeview(system_list_frame, columns=system_columns, show="headings", height=12)

        for col in system_columns:
            self.system_tree.heading(col, text=col)

        self.system_tree.column("名称", width=120)
        self.system_tree.column("协议格式", width=150)
        self.system_tree.column("描述", width=200)
        self.system_tree.column("类型", width=80)

        system_v_scrollbar = ttk.Scrollbar(system_list_frame, orient=tk.VERTICAL, command=self.system_tree.yview)
        system_h_scrollbar = ttk.Scrollbar(system_list_frame, orient=tk.HORIZONTAL, command=self.system_tree.xview)
        self.system_tree.configure(yscrollcommand=system_v_scrollbar.set, xscrollcommand=system_h_scrollbar.set)

        self.system_tree.grid(row=0, column=0, sticky="nsew")
        system_v_scrollbar.grid(row=0, column=1, sticky="ns")
        system_h_scrollbar.grid(row=1, column=0, sticky="ew")

        system_list_frame.grid_rowconfigure(0, weight=1)
        system_list_frame.grid_columnconfigure(0, weight=1)

        # 绑定双击事件 - 双击执行系统指令
        self.system_tree.bind("<Double-1>", lambda event: self.execute_system_command())

        # 系统指令按钮
        system_btn_frame = ttk.Frame(system_frame)
        system_btn_frame.pack(fill=tk.X)

        ttk.Button(system_btn_frame, text="刷新列表", command=self.refresh_system_commands).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(system_btn_frame, text="查看详情", command=self.view_system_command_detail).pack(side=tk.LEFT, padx=(0, 5))

        # 创建提醒列表界面
        self.create_reminder_interface(reminder_frame)

    def create_reminder_interface(self, parent):
        """创建提醒管理界面"""
        # 提醒数据表格 - 固定高度
        reminder_list_frame = ttk.Frame(parent)
        reminder_list_frame.pack(fill=tk.X, expand=False, pady=(0, 5))

        # 提醒数据列
        reminder_columns = ("ID", "标题", "触发时间", "类型", "子类型", "状态", "剩余时间")
        self.reminder_tree = ttk.Treeview(reminder_list_frame, columns=reminder_columns, show="headings", height=10)

        for col in reminder_columns:
            self.reminder_tree.heading(col, text=col)

        self.reminder_tree.column("ID", width=50)
        self.reminder_tree.column("标题", width=150)
        self.reminder_tree.column("触发时间", width=150)
        self.reminder_tree.column("类型", width=80)
        self.reminder_tree.column("子类型", width=80)
        self.reminder_tree.column("状态", width=80)
        self.reminder_tree.column("剩余时间", width=100)

        reminder_v_scrollbar = ttk.Scrollbar(reminder_list_frame, orient=tk.VERTICAL, command=self.reminder_tree.yview)
        reminder_h_scrollbar = ttk.Scrollbar(reminder_list_frame, orient=tk.HORIZONTAL, command=self.reminder_tree.xview)
        self.reminder_tree.configure(yscrollcommand=reminder_v_scrollbar.set, xscrollcommand=reminder_h_scrollbar.set)

        self.reminder_tree.grid(row=0, column=0, sticky="nsew")
        reminder_v_scrollbar.grid(row=0, column=1, sticky="ns")
        reminder_h_scrollbar.grid(row=1, column=0, sticky="ew")

        reminder_list_frame.grid_rowconfigure(0, weight=1)
        reminder_list_frame.grid_columnconfigure(0, weight=1)

        # 绑定右键菜单
        self.reminder_tree.bind("<Button-3>", self.show_reminder_context_menu)
        # 绑定双击查看详情
        self.reminder_tree.bind("<Double-1>", lambda event: self.view_reminder_detail())

        # 操作按钮区域
        btn_frame = ttk.Frame(parent)
        btn_frame.pack(fill=tk.X, pady=(0, 5))

        # 主要操作按钮
        ttk.Button(btn_frame, text="➕添加提醒", command=self.add_reminder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="✏️编辑选中", command=self.edit_selected_reminder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="🗑️删除选中", command=self.delete_selected_reminder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="🔄刷新列表", command=self.refresh_reminder_list).pack(side=tk.LEFT, padx=(0, 5))

        # 系统操作按钮
        ttk.Button(btn_frame, text="📊查看统计", command=self.show_reminder_status).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="⚙️系统状态", command=self.show_system_monitor).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="🧹清理完成", command=self.cleanup_completed).pack(side=tk.LEFT, padx=(0, 5))

        # 提醒创建区域
        create_frame = ttk.LabelFrame(parent, text="📝 创建提醒")
        create_frame.pack(fill=tk.X, pady=(0, 5))

        # 第一行：提醒文本
        text_row = ttk.Frame(create_frame)
        text_row.pack(fill=tk.X, padx=5, pady=(5, 2))

        ttk.Label(text_row, text="提醒文本:", width=8).pack(side=tk.LEFT)
        self.reminder_text_var = tk.StringVar()
        self.reminder_text_entry = ttk.Entry(text_row, textvariable=self.reminder_text_var, width=40)
        self.reminder_text_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)

        # 设置占位符效果
        self.setup_placeholder_text()

        # 第二行：时间和类型选择
        control_row = ttk.Frame(create_frame)
        control_row.pack(fill=tk.X, padx=5, pady=(2, 5))

        # 时间选择
        ttk.Label(control_row, text="时间:", width=8).pack(side=tk.LEFT)

        # 时间类型选择（分钟后 或 具体时间）
        self.time_mode_var = tk.StringVar(value="minutes")
        time_mode_frame = ttk.Frame(control_row)
        time_mode_frame.pack(side=tk.LEFT, padx=(5, 10))

        ttk.Radiobutton(time_mode_frame, text="分钟后", variable=self.time_mode_var,
                       value="minutes", command=self.on_time_mode_changed).pack(side=tk.LEFT)
        ttk.Radiobutton(time_mode_frame, text="具体时间", variable=self.time_mode_var,
                       value="time", command=self.on_time_mode_changed).pack(side=tk.LEFT, padx=(10, 0))

        # 分钟输入框
        self.minutes_frame = ttk.Frame(control_row)
        self.minutes_frame.pack(side=tk.LEFT, padx=(0, 10))

        self.minutes_var = tk.StringVar(value="30")
        self.minutes_spinbox = ttk.Spinbox(self.minutes_frame, textvariable=self.minutes_var,
                                          from_=1, to=1440, width=6)
        self.minutes_spinbox.pack(side=tk.LEFT)
        ttk.Label(self.minutes_frame, text="分钟").pack(side=tk.LEFT, padx=(2, 0))

        # 具体时间输入框（包含年月日）
        self.time_frame = ttk.Frame(control_row)

        # 年月日选择
        from datetime import datetime
        today = datetime.now()

        self.year_var = tk.StringVar(value=str(today.year))
        self.month_var = tk.StringVar(value=str(today.month))
        self.day_var = tk.StringVar(value=str(today.day))
        self.hour_var = tk.StringVar(value="21")
        self.minute_var = tk.StringVar(value="00")

        ttk.Spinbox(self.time_frame, textvariable=self.year_var, from_=2024, to=2030,
                   width=5).pack(side=tk.LEFT)
        ttk.Label(self.time_frame, text="年").pack(side=tk.LEFT)

        ttk.Spinbox(self.time_frame, textvariable=self.month_var, from_=1, to=12,
                   width=3, format="%02.0f").pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(self.time_frame, text="月").pack(side=tk.LEFT)

        ttk.Spinbox(self.time_frame, textvariable=self.day_var, from_=1, to=31,
                   width=3, format="%02.0f").pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(self.time_frame, text="日").pack(side=tk.LEFT)

        ttk.Spinbox(self.time_frame, textvariable=self.hour_var, from_=0, to=23,
                   width=3, format="%02.0f").pack(side=tk.LEFT, padx=(10, 0))
        ttk.Label(self.time_frame, text=":").pack(side=tk.LEFT)
        ttk.Spinbox(self.time_frame, textvariable=self.minute_var, from_=0, to=59,
                   width=3, format="%02.0f").pack(side=tk.LEFT)

        # 提醒类型选择（移到时间后面）
        ttk.Label(control_row, text="类型:", width=6).pack(side=tk.LEFT, padx=(20, 5))
        self.reminder_type_var = tk.StringVar(value="once")

        # 创建类型映射
        self.type_display_map = {
            "once": "一次性",
            "daily": "每日",
            "weekly": "每周",
            "monthly": "每月"
        }

        type_combo = ttk.Combobox(control_row, textvariable=self.reminder_type_var,
                                 values=list(self.type_display_map.keys()), width=8, state="readonly")
        type_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 绑定选择事件来更新显示
        type_combo.bind('<<ComboboxSelected>>', self.on_type_selected)

        # 周几选择框（weekly用）
        self.weekday_frame = ttk.Frame(control_row)
        ttk.Label(self.weekday_frame, text="周").pack(side=tk.LEFT)
        self.weekday_var = tk.StringVar(value="1")
        weekday_combo = ttk.Combobox(self.weekday_frame, textvariable=self.weekday_var,
                                   values=["1", "2", "3", "4", "5", "6", "7"], width=3, state="readonly")
        weekday_combo.pack(side=tk.LEFT, padx=(2, 0))

        # 月日选择框（monthly用）
        self.monthday_frame = ttk.Frame(control_row)
        ttk.Label(self.monthday_frame, text="每月").pack(side=tk.LEFT)
        self.monthday_var = tk.StringVar(value="1")
        monthday_combo = ttk.Combobox(self.monthday_frame, textvariable=self.monthday_var,
                                    values=[str(i) for i in range(1, 32)], width=3, state="readonly")
        monthday_combo.pack(side=tk.LEFT, padx=(2, 0))
        ttk.Label(self.monthday_frame, text="日").pack(side=tk.LEFT)

        # 创建按钮
        create_btn = ttk.Button(control_row, text="🔔 创建提醒", command=self.create_custom_reminder)
        create_btn.pack(side=tk.RIGHT, padx=(10, 5))

        # 初始化时间模式显示
        self.on_time_mode_changed()

        # 系统信息区域
        self.info_frame = ttk.LabelFrame(parent, text="📊 系统信息")
        self.info_frame.pack(fill=tk.X)

        self.info_label = ttk.Label(self.info_frame, text="总提醒数: 0 | 活跃: 0 | 已完成: 0 | 内存: 0KB")
        self.info_label.pack(padx=5, pady=2)

        # 初始化提醒列表
        self.load_reminder_data()

    def setup_placeholder_text(self):
        """设置占位符文本效果"""
        try:
            placeholder_text = "请输入提醒内容"

            # 初始设置占位符
            self.reminder_text_var.set(placeholder_text)
            self.reminder_text_entry.config(foreground='gray')
            self.is_placeholder = True

            def on_focus_in(event):
                """获得焦点时清空占位符"""
                if self.is_placeholder:
                    self.reminder_text_var.set("")
                    self.reminder_text_entry.config(foreground='black')
                    self.is_placeholder = False

            def on_focus_out(event):
                """失去焦点时恢复占位符（如果为空）"""
                if not self.reminder_text_var.get().strip():
                    self.reminder_text_var.set(placeholder_text)
                    self.reminder_text_entry.config(foreground='gray')
                    self.is_placeholder = True

            # 绑定事件
            self.reminder_text_entry.bind('<FocusIn>', on_focus_in)
            self.reminder_text_entry.bind('<FocusOut>', on_focus_out)

        except Exception as e:
            self.logger.error(f"设置占位符文本失败: {e}")

    def on_time_mode_changed(self):
        """时间模式改变时的处理"""
        try:
            if self.time_mode_var.get() == "minutes":
                # 显示分钟输入框，隐藏时间输入框
                self.minutes_frame.pack(side=tk.LEFT, padx=(0, 10))
                self.time_frame.pack_forget()
            else:
                # 显示时间输入框，隐藏分钟输入框
                self.minutes_frame.pack_forget()
                self.time_frame.pack(side=tk.LEFT, padx=(0, 10))
        except Exception as e:
            self.logger.error(f"切换时间模式失败: {e}")

    def on_type_selected(self, event=None):
        """类型选择改变时的处理"""
        try:
            selected_type = self.reminder_type_var.get()

            # 隐藏所有额外选择框
            self.weekday_frame.pack_forget()
            self.monthday_frame.pack_forget()

            # 根据类型调整界面
            if selected_type == "once":
                # 一次性提醒：显示年月日和时间
                self.time_mode_var.set("time")
                self.on_time_mode_changed()
                # 显示完整的年月日选择
                self.show_full_datetime()
            elif selected_type == "daily":
                # 每日提醒：只显示时间
                self.time_mode_var.set("time")
                self.on_time_mode_changed()
                # 隐藏年月日选择
                self.hide_date_selection()
            elif selected_type == "weekly":
                # 每周提醒：显示周几选择和时间
                self.time_mode_var.set("time")
                self.on_time_mode_changed()
                self.hide_date_selection()
                self.weekday_frame.pack(side=tk.LEFT, padx=(10, 0))
            elif selected_type == "monthly":
                # 每月提醒：显示日期选择和时间
                self.time_mode_var.set("time")
                self.on_time_mode_changed()
                self.hide_date_selection()
                self.monthday_frame.pack(side=tk.LEFT, padx=(10, 0))
        except Exception as e:
            self.logger.error(f"处理类型选择失败: {e}")

    def show_full_datetime(self):
        """显示完整的年月日时间选择"""
        try:
            # 确保年月日选择器可见
            for widget in self.time_frame.winfo_children():
                widget.pack(side=tk.LEFT)
        except Exception as e:
            self.logger.error(f"显示完整日期时间失败: {e}")

    def hide_date_selection(self):
        """隐藏年月日选择，只保留时分"""
        try:
            # 隐藏年月日相关的控件，只保留时分
            children = list(self.time_frame.winfo_children())
            # 前6个是年月日相关的控件，后面是时分
            for i, widget in enumerate(children):
                if i < 6:  # 年、月、日及其标签
                    widget.pack_forget()
                else:  # 时、分保持显示
                    widget.pack(side=tk.LEFT)
        except Exception as e:
            self.logger.error(f"隐藏日期选择失败: {e}")

    def create_custom_reminder(self):
        """创建自定义提醒"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            # 获取提醒文本
            reminder_text = self.reminder_text_var.get().strip()
            if not reminder_text or reminder_text == "请输入提醒内容" or self.is_placeholder:
                messagebox.showerror("错误", "请输入提醒内容")
                return

            # 获取提醒类型
            reminder_type = self.reminder_type_var.get()

            # 构建提醒数据
            reminder_data = {
                "title": reminder_text,
                "type": reminder_type
            }

            # 根据提醒类型设置时间
            if reminder_type == "once":
                # 一次性提醒：需要完整的年月日时间
                if self.time_mode_var.get() == "minutes":
                    try:
                        minutes = int(self.minutes_var.get())
                        if minutes <= 0:
                            messagebox.showerror("错误", "分钟数必须大于0")
                            return
                        reminder_data["minutes"] = minutes
                    except ValueError:
                        messagebox.showerror("错误", "请输入有效的分钟数")
                        return
                else:
                    try:
                        year = int(self.year_var.get())
                        month = int(self.month_var.get())
                        day = int(self.day_var.get())
                        hour = int(self.hour_var.get())
                        minute = int(self.minute_var.get())

                        # 验证日期时间有效性
                        if not (2024 <= year <= 2030):
                            messagebox.showerror("错误", "年份必须在2024-2030之间")
                            return
                        if not (1 <= month <= 12):
                            messagebox.showerror("错误", "月份必须在1-12之间")
                            return
                        if not (1 <= day <= 31):
                            messagebox.showerror("错误", "日期必须在1-31之间")
                            return
                        if not (0 <= hour <= 23 and 0 <= minute <= 59):
                            messagebox.showerror("错误", "请输入有效的时间（小时0-23，分钟0-59）")
                            return

                        # 验证日期是否存在
                        from datetime import datetime
                        try:
                            datetime(year, month, day, hour, minute)
                        except ValueError:
                            messagebox.showerror("错误", "请输入有效的日期")
                            return

                        reminder_data["datetime"] = f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:00"
                    except ValueError:
                        messagebox.showerror("错误", "请输入有效的日期时间")
                        return

            elif reminder_type == "daily":
                # 每日提醒：只需要时间
                try:
                    hour = int(self.hour_var.get())
                    minute = int(self.minute_var.get())
                    if not (0 <= hour <= 23 and 0 <= minute <= 59):
                        messagebox.showerror("错误", "请输入有效的时间（小时0-23，分钟0-59）")
                        return
                    reminder_data["time"] = f"{hour:02d}:{minute:02d}:00"
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的时间")
                    return

            elif reminder_type == "weekly":
                # 每周提醒：需要周几和时间
                try:
                    weekday = int(self.weekday_var.get())
                    hour = int(self.hour_var.get())
                    minute = int(self.minute_var.get())
                    if not (1 <= weekday <= 7):
                        messagebox.showerror("错误", "周几必须在1-7之间")
                        return
                    if not (0 <= hour <= 23 and 0 <= minute <= 59):
                        messagebox.showerror("错误", "请输入有效的时间（小时0-23，分钟0-59）")
                        return
                    reminder_data["weekday"] = weekday
                    reminder_data["time"] = f"{hour:02d}:{minute:02d}:00"
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的周几和时间")
                    return

            elif reminder_type == "monthly":
                # 每月提醒：需要日期和时间
                try:
                    monthday = int(self.monthday_var.get())
                    hour = int(self.hour_var.get())
                    minute = int(self.minute_var.get())
                    if not (1 <= monthday <= 31):
                        messagebox.showerror("错误", "日期必须在1-31之间")
                        return
                    if not (0 <= hour <= 23 and 0 <= minute <= 59):
                        messagebox.showerror("错误", "请输入有效的时间（小时0-23，分钟0-59）")
                        return
                    reminder_data["monthday"] = monthday
                    reminder_data["time"] = f"{hour:02d}:{minute:02d}:00"
                except ValueError:
                    messagebox.showerror("错误", "请输入有效的日期和时间")
                    return

            # 在后台线程中创建提醒
            def create_thread():
                try:
                    success = self.command_manager.create_reminder(reminder_data)
                    if success:
                        self.root.after(0, lambda: self.logger.info(f"提醒创建成功: {reminder_text}"))
                        self.root.after(0, lambda: messagebox.showinfo("成功", f"提醒 '{reminder_text}' 创建成功"))
                        # 刷新提醒列表
                        self.root.after(100, self.refresh_reminder_list)
                        # 清空输入框
                        self.root.after(0, self.clear_reminder_inputs)
                    else:
                        self.root.after(0, lambda: messagebox.showerror("错误", f"提醒 '{reminder_text}' 创建失败"))
                except Exception as thread_error:
                    error_msg = str(thread_error)
                    self.root.after(0, lambda: messagebox.showerror("错误", f"创建提醒失败: {error_msg}"))

            threading.Thread(target=create_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"创建自定义提醒失败: {e}")
            messagebox.showerror("错误", f"创建提醒失败: {e}")

    def clear_reminder_inputs(self):
        """清空提醒输入框"""
        try:
            # 恢复占位符文本
            self.reminder_text_var.set("请输入提醒内容")
            self.reminder_text_entry.config(foreground='gray')
            self.is_placeholder = True

            # 重置时间相关字段
            self.minutes_var.set("30")

            # 重置日期时间为今天
            from datetime import datetime
            today = datetime.now()
            self.year_var.set(str(today.year))
            self.month_var.set(str(today.month))
            self.day_var.set(str(today.day))
            self.hour_var.set("21")
            self.minute_var.set("00")

            # 重置其他选项
            self.reminder_type_var.set("once")
            self.time_mode_var.set("minutes")
            self.weekday_var.set("1")
            self.monthday_var.set("1")

            # 隐藏额外选择框
            self.weekday_frame.pack_forget()
            self.monthday_frame.pack_forget()

            self.on_time_mode_changed()
        except Exception as e:
            self.logger.error(f"清空输入框失败: {e}")

    def create_log_area(self, parent):
        """创建日志输出区域"""
        log_frame = ttk.Frame(parent)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 日志文本框 - 可扩展高度
        self.log_text = tk.Text(log_frame, wrap=tk.WORD, state=tk.DISABLED)
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 日志控制按钮
        log_btn_frame = ttk.Frame(parent)
        log_btn_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        ttk.Button(log_btn_frame, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(log_btn_frame, text="保存日志", command=self.save_logs).pack(side=tk.LEFT, padx=(0, 5))

    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

        # 状态标签
        self.status_label = ttk.Label(self.status_bar, text="就绪")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # 分隔符
        ttk.Separator(self.status_bar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)

        # 串口信息
        self.port_status_label = ttk.Label(self.status_bar, text="串口: 未连接")
        self.port_status_label.pack(side=tk.LEFT, padx=5)

        # 指令数量
        self.cmd_count_label = ttk.Label(self.status_bar, text="指令数: 0")
        self.cmd_count_label.pack(side=tk.RIGHT, padx=5)

    def load_settings(self):
        """加载设置"""
        try:
            # 加载上次使用的端口
            last_port = self.config_manager.get_last_port()
            if last_port and last_port in self.port_combo['values']:
                self.port_var.set(last_port)

            # 加载上次使用的波特率
            last_baudrate = self.config_manager.get_serial_baudrate()
            self.baudrate_var.set(str(last_baudrate))

            # 检查是否自动连接
            if self.config_manager.get_auto_connect() and last_port:
                self.root.after(1000, self.connect_device)  # 延迟1秒自动连接

        except Exception as e:
            self.logger.error(f"加载设置失败: {e}")

    def start_timers(self):
        """启动定时器"""
        # 日志更新定时器
        self.update_logs()

        # 状态刷新定时器 - 只有在连接设备且启用自动保持连接时才启动
        # 程序启动时不启动定时器，避免无意义的日志输出
        self.refresh_timer = None

    def auto_refresh(self):
        """自动刷新"""
        try:
            # 只有在连接状态且启用自动保持连接时才更新状态
            # 这样避免了用户禁用自动保持连接后仍然发送STATUS查询
            if self.serial_comm.is_port_connected() and self.auto_keepalive_var.get():
                self.update_status()

            # 重新设置定时器（只有在启用自动保持连接时）
            if self.config_manager.get_bool('ui', 'auto_refresh', True) and self.auto_keepalive_var.get():
                interval = self.config_manager.get_int('ui', 'refresh_interval', 5) * 1000
                self.refresh_timer = self.root.after(interval, self.auto_refresh)

        except Exception as e:
            self.logger.error(f"自动刷新失败: {e}")

    def refresh_ports(self):
        """刷新串口列表"""
        try:
            ports = self.serial_comm.get_available_ports()
            self.port_combo['values'] = ports

            # 如果当前选择的端口不在列表中，清空选择
            current_port = self.port_var.get()
            if current_port and current_port not in ports:
                self.port_var.set("")

            # 如果列表不为空且当前没有选择，选择第一个
            if ports and not self.port_var.get():
                self.port_var.set(ports[0])

            self.logger.info(f"发现 {len(ports)} 个可用串口")

        except Exception as e:
            self.logger.error(f"刷新串口列表失败: {e}")
            messagebox.showerror("错误", f"刷新串口列表失败: {e}")

    def refresh_all_data(self):
        """刷新所有数据（文本指令、系统指令、提醒列表）"""
        try:
            if not self.serial_comm.is_port_connected():
                # 如果未连接，只刷新串口列表
                self.refresh_ports()
                messagebox.showwarning("提示", "请先连接设备后再刷新数据")
                return

            self.logger.info("开始刷新所有数据...")

            # 顺序刷新所有数据，模拟连接成功后的初始化过程
            def refresh_all_thread():
                try:
                    # 第一步：获取文本指令
                    self.logger.info("刷新文本指令...")
                    text_commands = self.command_manager.get_text_commands()
                    self.root.after(0, self.update_command_tree, text_commands)

                    # 等待一小段时间确保第一个命令完全处理完毕
                    import time
                    time.sleep(0.5)

                    # 第二步：获取系统指令
                    self.logger.info("刷新系统指令...")
                    system_commands = self.command_manager.get_system_commands()
                    self.root.after(0, self.update_system_tree, system_commands)

                    # 等待一小段时间
                    time.sleep(0.5)

                    # 第三步：获取提醒列表
                    self.logger.info("刷新提醒列表...")
                    self.root.after(0, self.load_reminder_data)

                    self.logger.info("所有数据刷新完成")
                    self.root.after(0, lambda: messagebox.showinfo("成功", "所有数据刷新完成"))

                except Exception as e:
                    self.logger.error(f"刷新所有数据失败: {e}")
                    import traceback
                    self.logger.error(f"详细错误: {traceback.format_exc()}")
                    self.root.after(0, lambda: messagebox.showerror("错误", f"刷新数据失败: {e}"))

            # 在后台线程中执行
            threading.Thread(target=refresh_all_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"启动数据刷新失败: {e}")
            messagebox.showerror("错误", f"启动数据刷新失败: {e}")

    def connect_device(self):
        """连接设备"""
        try:
            port = self.port_var.get()
            if not port:
                messagebox.showerror("错误", "请选择串口")
                return

            if not Validators.validate_serial_port(port):
                messagebox.showerror("错误", "无效的串口名称")
                return

            # 获取界面选择的波特率
            baudrate_str = self.baudrate_var.get()
            if not baudrate_str:
                messagebox.showerror("错误", "请选择波特率")
                return

            try:
                baudrate = int(baudrate_str)
            except ValueError:
                messagebox.showerror("错误", "无效的波特率")
                return

            if not Validators.validate_baudrate(baudrate):
                messagebox.showerror("错误", "不支持的波特率")
                return

            # 在后台线程中连接
            def connect_thread():
                try:
                    if self.serial_comm.connect(port, baudrate):
                        self.root.after(0, self.on_connect_success, port, baudrate)
                    else:
                        self.root.after(0, self.on_connect_failed, "连接失败")
                except Exception as e:
                    self.root.after(0, self.on_connect_failed, str(e))

            threading.Thread(target=connect_thread, daemon=True).start()

            # 更新UI状态
            self.connect_btn.config(state=tk.DISABLED, text="连接中...")
            self.status_var.set("连接中...")

        except Exception as e:
            self.logger.error(f"连接设备失败: {e}")
            messagebox.showerror("错误", f"连接设备失败: {e}")

    def on_connect_success(self, port, baudrate):
        """连接成功回调"""
        try:
            self.connect_btn.config(state=tk.DISABLED, text="连接")
            self.disconnect_btn.config(state=tk.NORMAL)
            self.ping_btn.config(state=tk.NORMAL)
            self.status_var.set("●已连接")

            # 设置状态为绿色（不闪烁）
            if self.status_label_widget:
                pass  # 暂时去掉颜色设置

            # 保存端口和波特率设置
            self.config_manager.set_serial_port(port)
            self.config_manager.set_serial_baudrate(baudrate)
            self.config_manager.save_config()

            # 更新状态栏
            self.port_status_label.config(text=f"串口: {port} | 波特率: {baudrate}")
            self.status_label.config(text="已连接")

            # 顺序初始化数据 - 先获取文本指令，完成后再获取系统指令
            self.logger.info("连接成功，开始初始化数据...")
            self.root.after(100, self.initialize_data_sequentially)

            # 启动设备信息自动查询
            self.root.after(500, self.start_device_info_auto_query)

            # 更新汽车语音选项卡状态
            if self.car_voice_tab:
                self.car_voice_tab.set_serial_communication(self.serial_comm)

            # 启动保持连接定时器和自动刷新定时器
            if self.auto_keepalive_var.get():
                self.start_keepalive_timer()
                # 同时启动自动刷新定时器
                if self.config_manager.get_bool('ui', 'auto_refresh', True):
                    interval = self.config_manager.get_int('ui', 'refresh_interval', 5) * 1000
                    self.refresh_timer = self.root.after(interval, self.auto_refresh)

            self.logger.info(f"成功连接到 {port}，波特率: {baudrate}，数据初始化完成")

        except Exception as e:
            self.logger.error(f"连接成功处理失败: {e}")

    def initialize_data_sequentially(self):
        """顺序初始化数据 - 依次获取文本指令、系统指令、提醒列表"""
        def init_thread():
            import time

            # 第一步：获取文本指令（等待设备信息查询完成后再执行）
            try:
                self.logger.info("等待设备信息查询完成...")
                time.sleep(3.0)  # 等待设备信息查询完成

                self.logger.info("开始获取文本指令...")
                text_commands = self.command_manager.get_text_commands()
                if text_commands:
                    self.root.after(0, self.update_command_tree, text_commands)
                    self.logger.info("文本指令获取成功")
                else:
                    self.logger.warning("文本指令获取失败，但继续执行后续步骤")
            except Exception as e:
                self.logger.error(f"获取文本指令异常: {e}")
                import traceback
                self.logger.error(f"文本指令详细错误: {traceback.format_exc()}")

            # 等待一小段时间确保第一个命令完全处理完毕
            time.sleep(1.0)

            # 第二步：获取系统指令（等待所有查询完成后再执行）
            try:
                self.logger.info("开始获取系统指令...")
                # 等待更长时间确保所有查询完成，避免命令冲突
                time.sleep(5.0)
                system_commands = self.command_manager.get_system_commands()
                if system_commands:
                    self.root.after(0, self.update_system_tree, system_commands)
                    self.logger.info("系统指令获取成功")
                else:
                    self.logger.warning("系统指令获取失败")
            except Exception as e:
                self.logger.error(f"获取系统指令异常: {e}")
                import traceback
                self.logger.error(f"系统指令详细错误: {traceback.format_exc()}")

            # 等待一小段时间
            time.sleep(1.0)

            # 第三步：获取提醒列表
            try:
                self.logger.info("开始获取提醒列表...")
                self.root.after(0, self.load_reminder_data)
                self.logger.info("提醒列表获取任务已启动")
            except Exception as e:
                self.logger.error(f"获取提醒列表异常: {e}")
                import traceback
                self.logger.error(f"提醒列表详细错误: {traceback.format_exc()}")

            # 等待一小段时间
            time.sleep(0.5)

            # 第四步：查询汽车语音功能状态
            try:
                self.logger.info("开始查询汽车语音功能状态...")
                self.root.after(0, self.load_car_voice_status)
                self.logger.info("汽车语音状态查询任务已启动")
            except Exception as e:
                self.logger.error(f"查询汽车语音状态异常: {e}")
                import traceback
                self.logger.error(f"汽车语音状态详细错误: {traceback.format_exc()}")

            self.logger.info("数据初始化完成")

            # 如果系统指令获取失败，设置一个延迟重试
            if not system_commands:
                self.logger.info("系统指令获取失败，将在5秒后自动重试")
                self.root.after(5000, self.retry_system_commands)

        # 在后台线程中执行
        threading.Thread(target=init_thread, daemon=True).start()

    def retry_system_commands(self):
        """重试获取系统指令"""
        def retry_thread():
            try:
                self.logger.info("重试获取系统指令...")
                system_commands = self.command_manager.get_system_commands()
                if system_commands:
                    self.root.after(0, self.update_system_tree, system_commands)
                    self.logger.info("重试获取系统指令成功")
                else:
                    self.logger.warning("重试获取系统指令仍然失败")
            except Exception as e:
                self.logger.error(f"重试获取系统指令异常: {e}")

        threading.Thread(target=retry_thread, daemon=True).start()

    def initialize_basic_data(self):
        """启动时初始化基本数据（不依赖设备连接）"""
        def init_thread():
            try:
                self.logger.info("开始初始化基本数据...")

                # 检查是否已连接设备
                if not self.serial_comm.is_port_connected():
                    self.logger.info("设备未连接，跳过数据初始化")
                    return

                # 尝试获取文本指令（可能失败，但不影响后续步骤）
                try:
                    self.logger.info("尝试获取文本指令...")
                    text_commands = self.command_manager.get_text_commands()
                    if text_commands:
                        self.root.after(0, self.update_command_tree, text_commands)
                        self.logger.info("文本指令获取成功")
                    else:
                        self.logger.info("文本指令获取失败，将在连接设备后重试")
                except Exception as e:
                    self.logger.info(f"文本指令获取异常: {e}")

                # 等待一小段时间
                import time
                time.sleep(1.0)  # 增加等待时间

                # 尝试获取系统指令（可能失败，但不影响后续步骤）
                try:
                    self.logger.info("尝试获取系统指令...")
                    system_commands = self.command_manager.get_system_commands()
                    if system_commands:
                        self.root.after(0, self.update_system_tree, system_commands)
                        self.logger.info("系统指令获取成功")
                    else:
                        self.logger.info("系统指令获取失败，将在10秒后自动重试")
                        # 设置延迟重试
                        self.root.after(10000, self.retry_system_commands)
                except Exception as e:
                    self.logger.info(f"系统指令获取异常: {e}")
                    # 设置延迟重试
                    self.root.after(10000, self.retry_system_commands)

                self.logger.info("基本数据初始化完成")

            except Exception as e:
                self.logger.error(f"基本数据初始化失败: {e}")
                import traceback
                self.logger.error(f"详细错误: {traceback.format_exc()}")

        # 在后台线程中执行
        threading.Thread(target=init_thread, daemon=True).start()

    def on_connect_failed(self, error_msg):
        """连接失败回调"""
        self.connect_btn.config(state=tk.NORMAL, text="连接")
        self.disconnect_btn.config(state=tk.DISABLED)
        self.status_var.set("连接失败")
        self.status_label.config(text="连接失败")

        messagebox.showerror("连接失败", error_msg)
        self.logger.error(f"连接失败: {error_msg}")

    def disconnect_device(self):
        """断开设备"""
        try:
            self.serial_comm.disconnect()

            # 更新UI状态
            self.connect_btn.config(state=tk.NORMAL)
            self.disconnect_btn.config(state=tk.DISABLED)
            self.ping_btn.config(state=tk.DISABLED)
            self.status_var.set("未连接")
            self.text_count_var.set("0/50")
            self.system_count_var.set("0")
            self.memory_var.set("--KB")
            self.reminder_count_var.set("0")
            self.car_voice_status_var.set("未同步")
            if hasattr(self, 'car_voice_status_label'):
                self.car_voice_status_label.config(fg='orange')

            # 停止状态闪烁效果
            self.stop_status_blink()

            # 更新状态栏
            self.port_status_label.config(text="串口: 未连接")
            self.status_label.config(text="已断开")

            # 清空指令列表
            self.clear_command_tree()

            # 停止保持连接定时器
            self.stop_keepalive_timer()

            # 停止设备信息自动查询
            self.stop_device_info_auto_query()

            # 更新汽车语音选项卡状态
            if self.car_voice_tab:
                self.car_voice_tab.update_connection_status(False)

            self.logger.info("已断开连接")

        except Exception as e:
            self.logger.error(f"断开连接失败: {e}")
            messagebox.showerror("错误", f"断开连接失败: {e}")

    def ping_test(self):
        """PING连接测试"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            def ping_thread():
                try:
                    # 先尝试发送原始数据测试
                    self.logger.info("开始原始数据发送测试...")
                    raw_success = self.serial_comm.send_raw_data("PING\n")

                    if not raw_success:
                        self.root.after(0, lambda: messagebox.showerror("测试失败", "无法发送数据到串口"))
                        return

                    # 等待一下看是否有响应
                    import time
                    time.sleep(1)

                    # 然后尝试正常的PING测试
                    success = self.serial_comm.ping_test()
                    if success:
                        self.root.after(0, lambda: messagebox.showinfo("PING测试", "PING测试成功！设备响应正常"))
                    else:
                        self.root.after(0, lambda: messagebox.showerror("PING测试", "PING测试失败！设备无响应\n已发送原始数据，请检查ESP32设备状态"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("PING测试", f"PING测试异常: {e}"))

            threading.Thread(target=ping_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"PING测试失败: {e}")
            messagebox.showerror("错误", f"PING测试失败: {e}")

    def on_keepalive_changed(self):
        """自动保持连接选项改变"""
        try:
            if self.auto_keepalive_var.get():
                self.start_keepalive_timer()
                # 重新启动自动刷新定时器（如果配置启用）
                if self.config_manager.get_bool('ui', 'auto_refresh', True):
                    if self.refresh_timer:
                        self.root.after_cancel(self.refresh_timer)
                    interval = self.config_manager.get_int('ui', 'refresh_interval', 5) * 1000
                    self.refresh_timer = self.root.after(interval, self.auto_refresh)
                self.logger.info("启用自动保持连接")
            else:
                self.stop_keepalive_timer()
                # 停止自动刷新定时器，避免继续发送STATUS查询
                if self.refresh_timer:
                    self.root.after_cancel(self.refresh_timer)
                    self.refresh_timer = None
                self.logger.info("禁用自动保持连接，同时停止自动刷新")
        except Exception as e:
            self.logger.error(f"保持连接设置失败: {e}")

    def on_interval_changed(self):
        """保持连接间隔时间改变"""
        try:
            if self.auto_keepalive_var.get() and self.serial_comm.is_port_connected():
                # 重新启动定时器以应用新的间隔时间
                self.stop_keepalive_timer()
                self.start_keepalive_timer()
                interval = self.keepalive_interval_var.get()
                self.logger.info(f"保持连接间隔时间更改为 {interval} 秒")
        except Exception as e:
            self.logger.error(f"更改保持连接间隔失败: {e}")

    def start_keepalive_timer(self):
        """启动保持连接定时器"""
        try:
            if not self.serial_comm.is_port_connected() or not self.auto_keepalive_var.get():
                return

            interval = int(self.keepalive_interval_var.get()) * 1000  # 转换为毫秒
            self.keepalive_timer = self.root.after(interval, self.keepalive_check)
        except Exception as e:
            self.logger.error(f"启动保持连接定时器失败: {e}")

    def stop_keepalive_timer(self):
        """停止保持连接定时器"""
        try:
            if self.keepalive_timer:
                self.root.after_cancel(self.keepalive_timer)
                self.keepalive_timer = None
        except Exception as e:
            self.logger.error(f"停止保持连接定时器失败: {e}")

    def keepalive_check(self):
        """保持连接检查"""
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.warning("设备已断开，停止保持连接检查")
                return

            if not self.auto_keepalive_var.get():
                self.logger.debug("自动保持连接已禁用，停止检查")
                return

            # 发送STATUS命令保持连接
            def keepalive_thread():
                try:
                    response = self.serial_comm.send_command("STATUS")
                    if response and response.get("status") == "success":
                        self.logger.debug("保持连接检查成功")
                        # 更新系统状态
                        data = response.get("data", "")
                        status = self.command_manager._parse_system_status(data)
                        if status:
                            self.root.after(0, self.update_status_display, status)
                    else:
                        self.logger.warning("保持连接检查失败")
                except Exception as e:
                    self.logger.error(f"保持连接检查异常: {e}")

            threading.Thread(target=keepalive_thread, daemon=True).start()

            # 只有在启用自动保持连接时才设置下次检查
            if self.auto_keepalive_var.get():
                self.start_keepalive_timer()

        except Exception as e:
            self.logger.error(f"保持连接检查失败: {e}")

    def update_status_display(self, status):
        """更新状态显示"""
        try:
            text_count = status.get('text_cmds', 0)
            sys_count = status.get('sys_cmds', 13)
            free_memory = status.get('free_mem', 0)

            # 格式化内存显示
            if isinstance(free_memory, int):
                if free_memory > 1024:
                    memory_str = f"{free_memory//1024}KB"
                else:
                    memory_str = f"{free_memory}B"
            else:
                memory_str = str(free_memory)

            self.text_count_var.set(f"{text_count}/50")
            self.system_count_var.set(f"{sys_count}个")
            self.memory_var.set(memory_str)
        except Exception as e:
            self.logger.error(f"更新状态显示失败: {e}")

    def start_status_blink(self):
        """启动状态闪烁效果"""
        try:
            if self.status_label_widget:
                self.blink_state = True
                self.blink_status()
        except Exception as e:
            self.logger.error(f"启动状态闪烁失败: {e}")

    def stop_status_blink(self):
        """停止状态闪烁效果"""
        try:
            if self.blink_timer:
                self.root.after_cancel(self.blink_timer)
                self.blink_timer = None

            # 恢复正常显示
            if self.status_label_widget:
                pass  # 暂时去掉颜色设置
        except Exception as e:
            self.logger.error(f"停止状态闪烁失败: {e}")

    def blink_status(self):
        """状态闪烁效果"""
        try:
            if not self.serial_comm.is_port_connected() or not self.blink_state:
                return

            if self.status_label_widget:
                # 暂时去掉颜色闪烁
                pass

                # 设置下次闪烁
                self.blink_timer = self.root.after(500, self.blink_status)  # 500ms间隔
        except Exception as e:
            self.logger.error(f"状态闪烁失败: {e}")

    def refresh_commands(self):
        """刷新文本指令列表"""
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.warning("尝试刷新指令列表，但设备未连接")
                return

            self.logger.info("开始刷新文本指令列表...")
            # 在后台线程中获取指令列表
            def refresh_thread():
                try:
                    commands = self.command_manager.get_text_commands()
                    self.logger.info(f"获取到文本指令数据: {commands}")
                    self.root.after(0, self.update_command_tree, commands)
                except Exception as e:
                    import traceback
                    error_detail = traceback.format_exc()
                    self.logger.error(f"刷新指令列表失败: {e}")
                    self.logger.error(f"详细错误: {error_detail}")
                    self.root.after(0, lambda: self.logger.error(f"刷新指令列表失败: {e}"))

            threading.Thread(target=refresh_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"刷新指令列表失败: {e}")

    def refresh_system_commands(self):
        """刷新系统指令列表"""
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.warning("尝试刷新系统指令列表，但设备未连接")
                return

            self.logger.info("开始刷新系统指令列表...")
            def refresh_thread():
                try:
                    commands = self.command_manager.get_system_commands()
                    self.logger.info(f"获取到系统指令数据: {commands}")
                    self.root.after(0, self.update_system_tree, commands)
                except Exception as e:
                    import traceback
                    error_detail = traceback.format_exc()
                    self.logger.error(f"刷新系统指令列表失败: {e}")
                    self.logger.error(f"详细错误: {error_detail}")
                    self.root.after(0, lambda: self.logger.error(f"刷新系统指令列表失败: {e}"))

            threading.Thread(target=refresh_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"刷新系统指令列表失败: {e}")

    def update_command_tree(self, commands):
        """更新文本指令树"""
        try:
            self.logger.info(f"更新文本指令树，收到数据: {commands}")

            # 清空现有项目
            for item in self.cmd_tree.get_children():
                self.cmd_tree.delete(item)

            if commands:
                for name, text in commands.items():
                    self.cmd_tree.insert("", tk.END, values=(name, text))
                    self.logger.debug(f"添加文本指令到树: {name} = {text}")

                count = len(commands)
                self.text_count_var.set(f"{count}/50")
                self.cmd_count_label.config(text=f"指令数: {count}")
                self.logger.info(f"文本指令树更新完成，共 {count} 条指令")
            else:
                self.text_count_var.set("0/50")
                self.cmd_count_label.config(text="指令数: 0")
                self.logger.warning("文本指令数据为空，清空树显示")

        except Exception as e:
            self.logger.error(f"更新指令树失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def update_system_tree(self, commands):
        """更新系统指令树"""
        try:
            self.logger.info(f"更新系统指令树，收到数据: {commands}")

            # 清空现有项目
            for item in self.system_tree.get_children():
                self.system_tree.delete(item)

            if commands:
                for name, info in commands.items():
                    self.system_tree.insert("", tk.END, values=(
                        name,
                        info.get("protocol", ""),
                        info.get("description", ""),
                        info.get("type", "system")
                    ))
                    self.logger.debug(f"添加系统指令到树: {name} = {info}")

                self.system_count_var.set(f"{len(commands)}个")
                self.logger.info(f"系统指令树更新完成，共 {len(commands)} 条指令")
            else:
                self.system_count_var.set("0个")
                self.logger.warning("系统指令数据为空，清空树显示")

        except Exception as e:
            self.logger.error(f"更新系统指令树失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")

    def clear_command_tree(self):
        """清空指令树"""
        try:
            for item in self.cmd_tree.get_children():
                self.cmd_tree.delete(item)
            for item in self.system_tree.get_children():
                self.system_tree.delete(item)
        except:
            pass

    def update_status(self):
        """更新系统状态"""
        try:
            if not self.serial_comm.is_port_connected():
                return

            def status_thread():
                try:
                    status = self.command_manager.get_system_status()
                    if status:
                        text_count = status.get('text_cmds', 0)
                        sys_count = status.get('sys_cmds', 13)
                        free_memory = status.get('free_mem', 0)

                        # 格式化内存显示
                        if isinstance(free_memory, int):
                            if free_memory > 1024:
                                memory_str = f"{free_memory//1024}KB"
                            else:
                                memory_str = f"{free_memory}B"
                        else:
                            memory_str = str(free_memory)

                        self.root.after(0, lambda: self.text_count_var.set(f"{text_count}/50"))
                        self.root.after(0, lambda: self.system_count_var.set(f"{sys_count}个"))
                        self.root.after(0, lambda: self.memory_var.set(memory_str))
                except Exception as e:
                    self.root.after(0, lambda: self.logger.error(f"更新状态失败: {e}"))

            threading.Thread(target=status_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")

    def add_command(self):
        """添加指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            dialog = CommandDialog(self.root, self.command_manager)
            self.logger.info(f"添加指令对话框结果: {dialog.result}")
            if dialog.result:
                self.logger.info("添加指令成功，开始刷新列表...")
                self.root.after(100, self.refresh_commands)
            else:
                self.logger.info("添加指令被取消或失败，不刷新列表")

        except Exception as e:
            self.logger.error(f"添加指令失败: {e}")
            messagebox.showerror("错误", f"添加指令失败: {e}")

    def edit_command(self):
        """编辑指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            selection = self.cmd_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要编辑的指令")
                return

            item = self.cmd_tree.item(selection[0])
            values = item['values']

            if len(values) >= 2:
                name = values[0]
                text = values[1]

                dialog = CommandDialog(self.root, self.command_manager,
                                     edit_mode=True, name=name, text=text)
                self.logger.info(f"编辑指令对话框结果: {dialog.result}")
                if dialog.result:
                    self.logger.info("编辑指令成功，开始刷新列表...")
                    self.root.after(100, self.refresh_commands)
                else:
                    self.logger.info("编辑指令被取消或失败，不刷新列表")

        except Exception as e:
            self.logger.error(f"编辑指令失败: {e}")
            messagebox.showerror("错误", f"编辑指令失败: {e}")

    def delete_command(self):
        """删除指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            selection = self.cmd_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要删除的指令")
                return

            item = self.cmd_tree.item(selection[0])
            name = item['values'][0]

            # 直接删除，不需要确认对话框
            def delete_thread():
                try:
                    if self.command_manager.delete_command(name):
                        self.logger.info(f"指令删除成功: {name}")
                        self.root.after(100, self.refresh_commands)
                    else:
                        self.logger.error(f"指令删除失败: {name}")
                        self.root.after(0, lambda: messagebox.showerror("错误", f"指令 '{name}' 删除失败"))
                except Exception as e:
                    self.logger.error(f"删除指令异常: {e}")
                    self.root.after(0, lambda: messagebox.showerror("错误", f"删除指令失败: {e}"))

            threading.Thread(target=delete_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"删除指令失败: {e}")
            messagebox.showerror("错误", f"删除指令失败: {e}")

    def execute_command(self):
        """执行指令（按钮触发）"""
        self.execute_text_command()

    def execute_text_command(self):
        """执行文本指令（双击触发）"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            selection = self.cmd_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要执行的指令")
                return

            item = self.cmd_tree.item(selection[0])
            name = item['values'][0]

            def execute_thread():
                try:
                    self.logger.info(f"双击执行文本指令: {name}")
                    if self.command_manager.execute_command(name):
                        self.root.after(0, lambda: self.logger.info(f"文本指令 '{name}' 执行成功"))
                        # 执行指令不需要刷新列表，因为指令内容没有改变
                    else:
                        self.root.after(0, lambda: self.logger.error(f"文本指令 '{name}' 执行失败"))
                        self.root.after(0, lambda: messagebox.showerror("错误", f"指令 '{name}' 执行失败"))
                except Exception as e:
                    self.root.after(0, lambda: self.logger.error(f"执行文本指令失败: {e}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"执行指令失败: {e}"))

            threading.Thread(target=execute_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"执行文本指令失败: {e}")
            messagebox.showerror("错误", f"执行指令失败: {e}")

    def execute_system_command(self):
        """执行系统指令（双击触发）"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            selection = self.system_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要执行的系统指令")
                return

            item = self.system_tree.item(selection[0])
            values = item['values']
            name = values[0]
            protocol = values[1] if len(values) > 1 else name

            def execute_thread():
                try:
                    self.logger.info(f"双击执行系统指令: {name}")

                    # 发送协议格式指令
                    self.logger.info(f"发送协议指令: {protocol}")
                    response = self.serial_comm.send_command(protocol)
                    if response and response.get("status") == "success":
                        self.root.after(0, lambda: self.logger.info(f"系统指令 '{name}' (协议: {protocol}) 执行成功"))
                        # 如果是STATUS指令，更新状态显示
                        if protocol == "STATUS":
                            data = response.get("data", "")
                            status = self.command_manager._parse_system_status(data)
                            if status:
                                self.root.after(0, self.update_status_display, status)
                    else:
                        error_msg = response.get("error", "未知错误") if response else "无响应"
                        self.root.after(0, lambda: self.logger.error(f"系统指令 '{name}' (协议: {protocol}) 执行失败: {error_msg}"))
                        self.root.after(0, lambda: messagebox.showerror("错误", f"系统指令 '{name}' 执行失败: {error_msg}"))
                except Exception as e:
                    self.root.after(0, lambda: self.logger.error(f"执行系统指令失败: {e}"))
                    self.root.after(0, lambda: messagebox.showerror("错误", f"执行系统指令失败: {e}"))

            threading.Thread(target=execute_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"执行系统指令失败: {e}")
            messagebox.showerror("错误", f"执行系统指令失败: {e}")

    def reset_commands(self):
        """重置所有文本指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            # 确认重置
            if messagebox.askyesno("确认重置", "确定要删除所有文本指令吗？此操作不可恢复！"):
                def reset_thread():
                    try:
                        if self.command_manager.reset_commands():
                            self.root.after(0, lambda: messagebox.showinfo("成功", "所有文本指令已重置"))
                            self.root.after(100, self.refresh_commands)
                        else:
                            self.root.after(0, lambda: messagebox.showerror("错误", "重置指令失败"))
                    except Exception as e:
                        self.root.after(0, lambda: messagebox.showerror("错误", f"重置指令失败: {e}"))

                threading.Thread(target=reset_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"重置指令失败: {e}")
            messagebox.showerror("错误", f"重置指令失败: {e}")

    def view_system_command_detail(self):
        """查看系统指令详情"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            selection = self.system_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要查看的系统指令")
                return

            item = self.system_tree.item(selection[0])
            name = item['values'][0]

            def detail_thread():
                try:
                    detail = self.command_manager.get_system_command_detail(name)
                    if detail:
                        detail_text = f"指令名称: {detail['name']}\n\n描述: {detail['description']}\n\n类型: {detail['type']}"
                        self.root.after(0, lambda: self._show_detail_window(f"系统指令详情 - {name}", detail_text))
                    else:
                        self.root.after(0, lambda: messagebox.showerror("错误", "获取指令详情失败"))
                except Exception as e:
                    self.root.after(0, lambda: messagebox.showerror("错误", f"获取指令详情失败: {e}"))

            threading.Thread(target=detail_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"查看系统指令详情失败: {e}")
            messagebox.showerror("错误", f"查看系统指令详情失败: {e}")

    def _show_detail_window(self, title: str, content: str):
        """显示详情窗口"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title(title)
        detail_window.geometry("500x300")
        detail_window.transient(self.root)

        text_widget = tk.Text(detail_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(1.0, content)
        text_widget.config(state=tk.DISABLED)

    def backup_commands(self):
        """备份指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="保存备份文件",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                def backup_thread():
                    try:
                        backup_data = self.command_manager.backup_commands()
                        if backup_data:
                            with open(filename, 'w', encoding='utf-8') as f:
                                json.dump(backup_data, f, ensure_ascii=False, indent=2)

                            self.root.after(0, lambda: messagebox.showinfo("成功", f"备份保存到: {filename}"))
                        else:
                            self.root.after(0, lambda: messagebox.showerror("错误", "备份失败"))
                    except Exception as e:
                        self.root.after(0, lambda: messagebox.showerror("错误", f"保存备份失败: {e}"))

                threading.Thread(target=backup_thread, daemon=True).start()

        except Exception as e:
            self.logger.error(f"备份指令失败: {e}")
            messagebox.showerror("错误", f"备份指令失败: {e}")

    def restore_commands(self):
        """恢复指令"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            # 选择备份文件
            filename = filedialog.askopenfilename(
                title="选择备份文件",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filename:
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        backup_data = json.load(f)

                    # 确认恢复
                    if messagebox.askyesno("确认恢复", "恢复备份将覆盖现有的自定义指令，确定继续吗？"):
                        def restore_thread():
                            try:
                                if self.command_manager.restore_commands(backup_data):
                                    self.root.after(0, lambda: messagebox.showinfo("成功", "指令恢复成功"))
                                    self.root.after(100, self.refresh_commands)
                                else:
                                    self.root.after(0, lambda: messagebox.showerror("错误", "指令恢复失败"))
                            except Exception as e:
                                self.root.after(0, lambda: messagebox.showerror("错误", f"恢复指令失败: {e}"))

                        threading.Thread(target=restore_thread, daemon=True).start()

                except Exception as e:
                    messagebox.showerror("错误", f"读取备份文件失败: {e}")

        except Exception as e:
            self.logger.error(f"恢复指令失败: {e}")
            messagebox.showerror("错误", f"恢复指令失败: {e}")

    def import_commands(self):
        """导入指令"""
        self.restore_commands()  # 复用恢复功能

    def export_commands(self):
        """导出指令"""
        self.backup_commands()  # 复用备份功能

    def update_logs(self):
        """更新日志显示"""
        try:
            # 获取最新的日志记录
            recent_logs = self.gui_log_handler.get_recent_logs(50)

            # 检查是否有新日志（避免无意义的界面更新）
            current_content = self.log_text.get(1.0, tk.END).strip()
            new_content = '\n'.join(recent_logs).strip()

            # 只有内容发生变化时才更新界面
            if current_content != new_content:
                # 更新日志文本框
                self.log_text.config(state=tk.NORMAL)
                self.log_text.delete(1.0, tk.END)

                for log_record in recent_logs:
                    self.log_text.insert(tk.END, log_record + '\n')

                # 有新日志时自动滚动到底部
                self.log_text.see(tk.END)

                self.log_text.config(state=tk.DISABLED)

        except Exception as e:
            pass  # 避免日志更新错误影响程序运行

        # 设置下次更新 - 恢复1秒更新，但只有有变化时才刷新界面
        self.log_update_timer = self.root.after(1000, self.update_logs)

    def clear_logs(self):
        """清空日志"""
        try:
            self.log_manager.clear_log_records()
            self.log_text.config(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            self.log_text.config(state=tk.DISABLED)

            self.logger.info("日志已清空")

        except Exception as e:
            self.logger.error(f"清空日志失败: {e}")

    def save_logs(self):
        """保存日志"""
        try:
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                log_content = self.log_manager.get_log_file_content()
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                messagebox.showinfo("成功", f"日志已保存到: {filename}")

        except Exception as e:
            self.logger.error(f"保存日志失败: {e}")
            messagebox.showerror("错误", f"保存日志失败: {e}")

    def show_settings(self):
        """显示设置对话框"""
        try:
            dialog = SettingsDialog(self.root, self.config_manager)
            if dialog.result:
                # 重新加载配置
                self.load_settings()

        except Exception as e:
            self.logger.error(f"显示设置对话框失败: {e}")
            messagebox.showerror("错误", f"显示设置对话框失败: {e}")

    def show_help(self):
        """显示帮助"""
        try:
            if self.serial_comm.is_port_connected():
                def help_thread():
                    try:
                        help_text = self.command_manager.get_help()
                        if help_text:
                            self.root.after(0, lambda: self._show_help_window(help_text))
                        else:
                            self.root.after(0, lambda: self._show_default_help())
                    except Exception as e:
                        self.root.after(0, lambda: self._show_default_help())

                threading.Thread(target=help_thread, daemon=True).start()
            else:
                self._show_default_help()

        except Exception as e:
            self.logger.error(f"显示帮助失败: {e}")
            self._show_default_help()

    def _show_help_window(self, help_text):
        """显示帮助窗口"""
        help_window = tk.Toplevel(self.root)
        help_window.title("使用帮助")
        help_window.geometry("600x400")
        help_window.transient(self.root)

        text_widget = tk.Text(help_window, wrap=tk.WORD, padx=10, pady=10)
        scrollbar = ttk.Scrollbar(help_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        text_widget.insert(1.0, help_text)
        text_widget.config(state=tk.DISABLED)

    def _show_default_help(self):
        """显示默认帮助"""
        help_text = """
小智ESP32指令管理器使用说明

1. 连接设备
   - 选择正确的串口
   - 点击"连接"按钮
   - 确保ESP32设备已正确连接

2. 管理指令
   - 添加指令：点击"添加指令"按钮
   - 编辑指令：双击指令或选中后点击"编辑指令"
   - 删除指令：选中指令后点击"删除指令"
   - 执行指令：选中指令后点击"执行指令"

3. 备份恢复
   - 备份：点击"备份"按钮保存指令到文件
   - 恢复：点击"恢复"按钮从文件恢复指令

4. 设置
   - 点击"设置"按钮配置串口参数和界面选项

5. 注意事项
   - 确保串口参数正确（波特率19200）
   - 指令名称只能包含字母、数字、下划线和中文
   - 定期备份重要指令
        """
        self._show_help_window(help_text.strip())

    def show_about(self):
        """显示关于对话框"""
        about_text = """小智ESP32指令管理器 v1.0

一个用于管理小智AI设备自定义指令的GUI应用程序

功能特性：
• 自定义指令的增删改查
• 内置指令查看
• 指令执行和测试
• 配置备份和恢复
• 实时系统状态监控
• 日志记录和查看

技术栈：
• Python + tkinter
• pyserial 串口通信
• JSON 数据交换

开发者：AI Assistant
版本：1.0.0
        """
        messagebox.showinfo("关于", about_text.strip())

    def on_window_configure(self, event):
        """窗口配置变化事件"""
        try:
            # 只处理主窗口的配置变化
            if event.widget == self.root:
                # 保存窗口几何信息
                geometry = self.root.geometry()
                if 'x' in geometry and '+' in geometry:
                    size_part, pos_part = geometry.split('+', 1)
                    width, height = map(int, size_part.split('x'))
                    x, y = map(int, pos_part.split('+'))

                    if Validators.validate_window_geometry(width, height, x, y):
                        self.config_manager.set_window_geometry(width, height, x, y)

        except Exception as e:
            pass  # 忽略几何信息保存错误

    def on_closing(self):
        """窗口关闭事件"""
        try:
            # 保存配置
            self.config_manager.save_config()

            # 停止定时器
            if self.refresh_timer:
                self.root.after_cancel(self.refresh_timer)
            if self.log_update_timer:
                self.root.after_cancel(self.log_update_timer)
            if self.keepalive_timer:
                self.root.after_cancel(self.keepalive_timer)
            if self.blink_timer:
                self.root.after_cancel(self.blink_timer)

            # 断开串口连接
            if self.serial_comm.is_port_connected():
                self.serial_comm.disconnect()

            self.logger.info("应用程序正常退出")

        except Exception as e:
            self.logger.error(f"退出时出错: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """运行主程序"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()
        except Exception as e:
            self.logger.error(f"主程序运行异常: {e}")
            messagebox.showerror("严重错误", f"程序运行异常: {e}")
            self.on_closing()

    def load_reminder_data(self):
        """从ESP32加载提醒数据"""
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.warning("设备未连接，无法加载提醒数据")
                return

            self.logger.info("开始加载提醒数据...")

            # 发送获取提醒列表的指令
            response = self.serial_comm.send_command("REMINDER_LIST")

            if response and response.get("status") == "success":
                data = response.get("data", {})

                # 检查是否是新的响应格式（包含reminders字段）
                if isinstance(data, dict) and "reminders" in data:
                    # 新的响应格式：{"status": "success", "data": {"reminders": [...]}}
                    reminders = data["reminders"]
                    self.logger.info(f"检测到新响应格式，包含 {len(reminders)} 个提醒")

                    # 清空现有数据
                    self.reminder_data.clear()

                    # 直接使用解析好的提醒数据
                    for reminder in reminders:
                        reminder_id = reminder.get("id", "")
                        if reminder_id:
                            # 转换为内部格式
                            self.reminder_data[reminder_id] = {
                                "id": reminder_id,
                                "title": reminder.get("title", ""),
                                "time": reminder.get("time", ""),
                                "type": reminder.get("type", ""),
                                "status": "active"  # 默认状态
                            }

                elif isinstance(data, str):
                    # 旧的响应格式：直接返回字符串数据
                    if data == "no reminders":
                        self.logger.info("当前没有提醒数据")
                        self.reminder_data.clear()
                    elif "=" in data and "|" in data:
                        # 新格式: reminder_id=标题|时间|类型
                        self.logger.info("检测到新格式提醒数据，使用新解析方法")
                        self.parse_reminder_data_new_format(data)
                    else:
                        # 旧格式: ID:1|标题:开会|时间:2025-07-31 15:30:00|类型:once|状态:active
                        self.logger.info("检测到旧格式提醒数据，使用旧解析方法")
                        self.parse_reminder_data(data)
                else:
                    self.logger.warning(f"未知的数据格式: {type(data)}, 内容: {data}")
                    self.reminder_data.clear()

                self.update_reminder_display()
                self.logger.info("提醒数据加载完成")
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                self.logger.error(f"加载提醒数据失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"加载提醒数据异常: {e}")

    def load_car_voice_status(self):
        """查询汽车语音功能状态"""
        try:
            if not self.serial_comm.is_port_connected():
                self.logger.warning("设备未连接，无法查询汽车语音状态")
                return

            self.logger.info("开始查询汽车语音功能状态...")

            # 直接使用GET_CAR_VOICE_CONFIG命令获取所有配置
            self.logger.info("发送汽车语音配置查询命令...")

            def query_thread():
                try:
                    # 发送查询命令
                    command = "GET_CAR_VOICE_CONFIG"
                    response = self.serial_comm.send_command(command)

                    if response and response.get("status") == "success":
                        # 解析响应数据并更新UI
                        config_data = response.get("data", {})
                        self.root.after(0, self.update_car_voice_all_status, config_data)
                        self.logger.info("成功获取汽车语音配置")
                    else:
                        error_msg = response.get("error", "未知错误") if response else "无响应"
                        self.logger.warning(f"获取汽车语音配置失败: {error_msg}")

                except Exception as e:
                    self.logger.error(f"查询汽车语音配置异常: {e}")

                self.logger.info("汽车语音功能状态查询完成")

            # 在后台线程中执行查询
            import threading
            query_thread_obj = threading.Thread(target=query_thread, daemon=True)
            query_thread_obj.start()

        except Exception as e:
            self.logger.error(f"查询汽车语音状态失败: {e}")

    def update_car_voice_all_status(self, config_data):
        """更新所有汽车语音功能状态到UI"""
        try:
            # 查找汽车语音选项卡
            car_voice_tab = None
            for tab_id in self.notebook.tabs():
                tab_text = self.notebook.tab(tab_id, "text")
                if "汽车语音" in tab_text:
                    car_voice_tab = self.notebook.nametowidget(tab_id)
                    break

            if car_voice_tab and hasattr(car_voice_tab, 'update_all_status'):
                car_voice_tab.update_all_status(config_data)
                # 更新汽车语音状态显示
                self.car_voice_status_var.set("已同步")
                self.car_voice_status_label.config(fg='green')
                self.logger.info("已更新所有汽车语音功能状态到UI")
            else:
                # 更新汽车语音状态显示为未同步
                self.car_voice_status_var.set("未同步")
                self.car_voice_status_label.config(fg='orange')
                self.logger.warning("未找到汽车语音选项卡或更新方法")

        except Exception as e:
            self.logger.error(f"更新汽车语音功能状态到UI失败: {e}")


    def parse_reminder_type_detail(self, type_detail):
        """解析提醒类型详情，提取类型和额外参数"""
        try:
            if type_detail == "单次提醒":
                return {"type": "once"}
            elif type_detail == "每日提醒":
                return {"type": "daily"}
            elif type_detail.startswith("每周提醒(") and type_detail.endswith(")"):
                # 解析 "每周提醒(周一)" 格式
                weekday_text = type_detail[5:-1]  # 提取括号内的内容
                weekday_mapping = {
                    "周日": 0, "周一": 1, "周二": 2, "周三": 3,
                    "周四": 4, "周五": 5, "周六": 6
                }
                weekday = weekday_mapping.get(weekday_text, 1)
                return {"type": "weekly", "weekday": weekday}
            elif type_detail.startswith("每月提醒(") and type_detail.endswith(")"):
                # 解析 "每月提醒(1号)" 格式
                day_text = type_detail[5:-1]  # 提取括号内的内容
                if day_text.endswith("号"):
                    day_text = day_text[:-1]  # 去掉"号"字
                try:
                    monthday = int(day_text)
                    return {"type": "monthly", "monthday": monthday}
                except ValueError:
                    self.logger.warning(f"无法解析月日: {day_text}")
                    return {"type": "monthly"}
            elif type_detail.startswith("每年提醒(") and type_detail.endswith(")"):
                # 解析 "每年提醒(1月)" 格式
                month_text = type_detail[5:-1]  # 提取括号内的内容
                if month_text.endswith("月"):
                    month_text = month_text[:-1]  # 去掉"月"字
                try:
                    month = int(month_text)
                    return {"type": "yearly", "month": month}
                except ValueError:
                    self.logger.warning(f"无法解析月份: {month_text}")
                    return {"type": "yearly"}
            else:
                # 兼容旧格式
                type_mapping = {
                    "每周提醒": "weekly",
                    "每月提醒": "monthly",
                    "每年提醒": "yearly",
                    "倒计时提醒": "countdown"
                }
                reminder_type = type_mapping.get(type_detail, "once")
                return {"type": reminder_type}
        except Exception as e:
            self.logger.error(f"解析类型详情失败: {e}, 原始数据: {type_detail}")
            return {"type": "once"}

    def parse_reminder_data(self, data):
        """解析提醒数据"""
        try:
            self.reminder_data.clear()

            if not data or data.strip() == "":
                self.logger.info("没有提醒数据")
                return

            # 解析提醒数据格式
            # 假设格式: "ID:1|标题:开会|时间:2025-07-31 15:30:00|类型:once|状态:active"
            lines = data.strip().split('\n')
            for line in lines:
                if not line.strip():
                    continue

                reminder = self.parse_single_reminder(line)
                if reminder:
                    self.reminder_data[reminder["id"]] = reminder

            self.logger.info(f"解析到 {len(self.reminder_data)} 个提醒")

        except Exception as e:
            self.logger.error(f"解析提醒数据失败: {e}")

    def parse_reminder_data_new_format(self, data):
        """解析新格式的提醒数据"""
        try:
            self.logger.info(f"开始解析新格式提醒数据: {data}")
            self.reminder_data.clear()

            if not data or data.strip() == "":
                self.logger.info("没有提醒数据")
                return

            # 新格式: reminder_id1=标题1|2025-07-31 15:30:00|单次提醒,reminder_id2=标题2|09:00:00|每日提醒
            reminders = data.split(',')
            self.logger.info(f"分割后的提醒数据: {reminders}")

            for i, reminder_str in enumerate(reminders):
                self.logger.info(f"处理第{i+1}个提醒: {reminder_str}")

                if '=' in reminder_str:
                    reminder_id, reminder_info = reminder_str.split('=', 1)
                    reminder_id = reminder_id.strip()
                    self.logger.info(f"提醒ID: {reminder_id}, 信息: {reminder_info}")

                    # 解析提醒信息: 标题|时间|类型详情
                    parts = reminder_info.split('|')
                    self.logger.info(f"信息分割结果: {parts}, 长度: {len(parts)}")

                    if len(parts) >= 3:
                        title = parts[0].strip()
                        time = parts[1].strip()
                        type_detail = parts[2].strip()

                        self.logger.info(f"解析结果 - 标题: {title}, 时间: {time}, 类型详情: {type_detail}")

                        # 解析新格式的类型详情
                        reminder_obj = {
                            "id": reminder_id,
                            "title": title,
                            "time": time,
                            "status": "active"  # 默认为活跃状态
                        }

                        # 解析类型详情并提取额外信息
                        parsed_type = self.parse_reminder_type_detail(type_detail)
                        reminder_obj.update(parsed_type)

                        self.reminder_data[reminder_id] = reminder_obj
                        self.logger.info(f"成功添加提醒: {reminder_obj}")
                    else:
                        self.logger.warning(f"提醒信息格式不正确，部分数量: {len(parts)}, 内容: {parts}")
                else:
                    self.logger.warning(f"提醒字符串格式不正确，没有找到'='分隔符: {reminder_str}")

            self.logger.info(f"解析完成，共解析到 {len(self.reminder_data)} 个提醒（新格式）")
            self.logger.info(f"提醒数据详情: {self.reminder_data}")

        except Exception as e:
            import traceback
            self.logger.error(f"解析新格式提醒数据失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")

    def parse_single_reminder(self, line):
        """解析单个提醒数据"""
        try:
            # 解析格式: "ID:1|标题:开会|时间:2025-07-31 15:30:00|类型:once|状态:active"
            parts = line.split('|')
            reminder = {}

            for part in parts:
                if ':' in part:
                    key, value = part.split(':', 1)
                    key = key.strip()
                    value = value.strip()

                    if key == "ID":
                        reminder["id"] = value
                    elif key == "标题":
                        reminder["title"] = value
                    elif key == "时间":
                        reminder["time"] = value
                    elif key == "类型":
                        # 如果是新格式的类型详情，解析它
                        if any(x in value for x in ["每周提醒(", "每月提醒(", "每年提醒("]):
                            parsed_type = self.parse_reminder_type_detail(value)
                            reminder.update(parsed_type)
                        else:
                            # 旧格式的简单类型
                            type_mapping = {
                                "单次提醒": "once",
                                "每日提醒": "daily",
                                "每周提醒": "weekly",
                                "每月提醒": "monthly",
                                "每年提醒": "yearly"
                            }
                            reminder["type"] = type_mapping.get(value, value)
                    elif key == "状态":
                        reminder["status"] = value
                    elif key == "周几":
                        try:
                            reminder["weekday"] = int(value)
                        except ValueError:
                            self.logger.warning(f"无效的周几参数: {value}")
                    elif key == "月日":
                        try:
                            reminder["monthday"] = int(value)
                        except ValueError:
                            self.logger.warning(f"无效的月日参数: {value}")
                    elif key == "月份":
                        try:
                            reminder["month"] = int(value)
                        except ValueError:
                            self.logger.warning(f"无效的月份参数: {value}")

            # 验证必要字段
            if all(k in reminder for k in ["id", "title", "time", "type", "status"]):
                return reminder
            else:
                self.logger.warning(f"提醒数据不完整: {line}")
                return None

        except Exception as e:
            self.logger.error(f"解析单个提醒失败: {line}, 错误: {e}")
            return None

    def update_reminder_display(self):
        """更新提醒显示"""
        try:
            # 清空现有项目
            for item in self.reminder_tree.get_children():
                self.reminder_tree.delete(item)

            # 添加提醒数据
            for reminder_id, reminder in self.reminder_data.items():
                # 计算剩余时间
                remaining = self.calculate_remaining_time(reminder["time"], reminder["type"])

                # 获取子类型信息
                subtype = self.get_reminder_subtype(reminder)

                self.reminder_tree.insert("", tk.END, values=(
                    reminder["id"],
                    reminder["title"],
                    reminder["time"],
                    reminder["type"],
                    subtype,
                    reminder["status"],
                    remaining
                ))

            # 更新系统信息
            self.update_system_info()

            # 更新提醒数量显示
            self.reminder_count_var.set(f"{len(self.reminder_data)}")

            self.logger.info(f"提醒显示更新完成，共 {len(self.reminder_data)} 个提醒")

        except Exception as e:
            self.logger.error(f"更新提醒显示失败: {e}")

    def get_reminder_subtype(self, reminder):
        """获取提醒的子类型显示文本"""
        try:
            reminder_type = reminder.get("type", "once")

            if reminder_type == "once":
                # 一次性提醒不显示子类型
                return ""
            elif reminder_type == "daily":
                # 每日提醒不显示子类型
                return ""
            elif reminder_type == "weekly":
                # 每周提醒显示周几
                weekday = reminder.get("weekday", 1)
                weekday_names = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
                if 0 <= weekday <= 6:
                    return weekday_names[weekday]
                else:
                    return f"周{weekday}"
            elif reminder_type == "monthly":
                # 每月提醒显示几号
                monthday = reminder.get("monthday", 1)
                return f"{monthday}号"
            elif reminder_type == "yearly":
                # 每年提醒显示几月
                month = reminder.get("month", 1)
                return f"{month}月"
            else:
                return ""
        except Exception as e:
            self.logger.error(f"获取提醒子类型失败: {e}")
            return ""

    def calculate_remaining_time(self, time_str, reminder_type):
        """计算剩余时间"""
        try:
            from datetime import datetime, timedelta

            if reminder_type == "once":
                # 一次性提醒
                target_time = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                now = datetime.now()

                if target_time > now:
                    delta = target_time - now
                    if delta.days > 0:
                        return f"{delta.days}天"
                    elif delta.seconds > 3600:
                        return f"{delta.seconds // 3600}小时"
                    else:
                        return f"{delta.seconds // 60}分钟"
                else:
                    return "已过期"

            elif reminder_type == "daily":
                # 每日提醒
                return "每日"

            elif reminder_type == "weekly":
                # 每周提醒
                return "每周"

            else:
                return "未知"

        except Exception as e:
            self.logger.error(f"计算剩余时间失败: {e}")
            return "计算错误"

    def update_system_info(self):
        """更新系统信息"""
        try:
            total = len(self.reminder_data)
            active = sum(1 for r in self.reminder_data.values() if r["status"] == "active")
            completed = sum(1 for r in self.reminder_data.values() if r["status"] == "completed")

            # 估算内存使用（简单计算）
            memory_kb = total * 0.1  # 假设每个提醒占用0.1KB

            info_text = f"总提醒数: {total} | 活跃: {active} | 已完成: {completed} | 内存: {memory_kb:.1f}KB"
            self.info_label.config(text=info_text)

        except Exception as e:
            self.logger.error(f"更新系统信息失败: {e}")

    def show_reminder_context_menu(self, event):
        """显示提醒右键菜单"""
        try:
            # 获取选中的项目
            item = self.reminder_tree.identify_row(event.y)
            if item:
                self.reminder_tree.selection_set(item)

                # 创建右键菜单
                context_menu = tk.Menu(self.root, tearoff=0)
                context_menu.add_command(label="📝 查看详情", command=self.view_reminder_detail)
                context_menu.add_separator()
                context_menu.add_command(label="✏️ 编辑标题", command=lambda: self.edit_reminder_field("title"))
                context_menu.add_command(label="⏰ 修改时间", command=lambda: self.edit_reminder_field("time"))
                context_menu.add_command(label="🔄 修改类型", command=lambda: self.edit_reminder_field("type"))
                context_menu.add_separator()
                context_menu.add_command(label="✅ 启用提醒", command=lambda: self.toggle_reminder(True))
                context_menu.add_command(label="❌ 禁用提醒", command=lambda: self.toggle_reminder(False))
                context_menu.add_separator()
                context_menu.add_command(label="📋 复制提醒", command=self.copy_reminder)
                context_menu.add_command(label="⏰ 延迟提醒", command=self.snooze_reminder)
                context_menu.add_separator()
                context_menu.add_command(label="🗑️ 删除提醒", command=self.delete_selected_reminder)

                # 显示菜单
                context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            self.logger.error(f"显示右键菜单失败: {e}")

    def view_reminder_detail(self):
        """查看提醒详情"""
        try:
            selection = self.reminder_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要查看的提醒")
                return

            item = self.reminder_tree.item(selection[0])
            reminder_id = item['values'][0]

            # 发送查看详情指令
            response = self.serial_comm.send_command(f"REMINDER_GET {reminder_id}")

            if response and response.get("status") == "success":
                data = response.get("data", "无详情信息")
                messagebox.showinfo("提醒详情", data)
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                messagebox.showerror("错误", f"获取提醒详情失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"查看提醒详情失败: {e}")
            messagebox.showerror("错误", f"查看提醒详情失败: {e}")

    def add_reminder(self):
        """添加新提醒"""
        try:
            if not self.serial_comm.is_port_connected():
                messagebox.showerror("错误", "请先连接设备")
                return

            # 创建添加提醒对话框
            self.show_add_reminder_dialog()

        except Exception as e:
            self.logger.error(f"添加提醒失败: {e}")
            messagebox.showerror("错误", f"添加提醒失败: {e}")

    def edit_selected_reminder(self):
        """编辑选中的提醒"""
        try:
            selection = self.reminder_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要编辑的提醒")
                return

            item = self.reminder_tree.item(selection[0])
            reminder_id = item['values'][0]

            # 获取当前提醒信息
            if reminder_id in self.reminder_data:
                reminder = self.reminder_data[reminder_id]
                self.show_edit_reminder_dialog(reminder)
            else:
                messagebox.showerror("错误", "找不到提醒信息")

        except Exception as e:
            self.logger.error(f"编辑提醒失败: {e}")
            messagebox.showerror("错误", f"编辑提醒失败: {e}")

    def delete_selected_reminder(self):
        """删除选中的提醒"""
        try:
            selection = self.reminder_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要删除的提醒")
                return

            item = self.reminder_tree.item(selection[0])
            reminder_id = item['values'][0]
            reminder_title = item['values'][1]

            # 确认删除
            if messagebox.askyesno("确认删除", f"确定要删除提醒 '{reminder_title}' 吗？"):
                response = self.serial_comm.send_command(f"REMINDER_DEL {reminder_id}")

                if response and response.get("status") == "success":
                    self.logger.info(f"删除提醒成功: {reminder_title}")
                    # 自动刷新提醒列表
                    self.root.after(100, self.refresh_reminder_list)
                    messagebox.showinfo("成功", "提醒删除成功")
                else:
                    error_msg = response.get("error", "未知错误") if response else "无响应"
                    messagebox.showerror("错误", f"删除提醒失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"删除提醒失败: {e}")
            messagebox.showerror("错误", f"删除提醒失败: {e}")

    def refresh_reminder_list(self):
        """刷新提醒列表"""
        self.load_reminder_data()

    def show_reminder_status(self):
        """显示提醒统计"""
        try:
            response = self.serial_comm.send_command("reminder status")

            if response and response.get("status") == "success":
                data = response.get("data", "无统计信息")
                messagebox.showinfo("提醒统计", data)
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                messagebox.showerror("错误", f"获取提醒统计失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"显示提醒统计失败: {e}")
            messagebox.showerror("错误", f"显示提醒统计失败: {e}")

    def show_system_monitor(self):
        """显示系统监控"""
        try:
            response = self.serial_comm.send_command("reminder monitor")

            if response and response.get("status") == "success":
                data = response.get("data", "无监控信息")
                messagebox.showinfo("系统监控", data)
            else:
                error_msg = response.get("error", "未知错误") if response else "无响应"
                messagebox.showerror("错误", f"获取系统监控失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"显示系统监控失败: {e}")
            messagebox.showerror("错误", f"显示系统监控失败: {e}")

    def cleanup_completed(self):
        """清理已完成的提醒"""
        try:
            if messagebox.askyesno("确认清理", "确定要删除所有已完成的提醒吗？"):
                response = self.serial_comm.send_command("REMINDER_CLEAR")

                if response and response.get("status") == "success":
                    self.logger.info("清理已完成提醒成功")
                    # 自动刷新提醒列表
                    self.root.after(100, self.refresh_reminder_list)
                    messagebox.showinfo("成功", "已完成提醒清理成功")
                else:
                    error_msg = response.get("error", "未知错误") if response else "无响应"
                    messagebox.showerror("错误", f"清理已完成提醒失败: {error_msg}")

        except Exception as e:
            self.logger.error(f"清理已完成提醒失败: {e}")
            messagebox.showerror("错误", f"清理已完成提醒失败: {e}")





    def copy_reminder(self):
        """复制提醒 - 暂不支持，显示提示"""
        messagebox.showinfo("提示", "复制功能暂不支持，请手动创建新提醒")

    def snooze_reminder(self):
        """延迟提醒 - 暂不支持，显示提示"""
        messagebox.showinfo("提示", "延迟功能暂不支持，请编辑提醒时间")

    def show_add_reminder_dialog(self):
        """显示添加提醒对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self.root)
            dialog.title("添加新提醒")
            dialog.geometry("450x450")
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.resizable(False, False)

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
            y = (dialog.winfo_screenheight() // 2) - (450 // 2)
            dialog.geometry(f"450x450+{x}+{y}")

            # 主框架
            main_frame = ttk.Frame(dialog)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题输入
            ttk.Label(main_frame, text="提醒标题:").pack(anchor=tk.W, pady=(0, 5))
            title_entry = ttk.Entry(main_frame, width=50)
            title_entry.pack(fill=tk.X, pady=(0, 10))
            title_entry.focus_set()

            # 类型选择
            ttk.Label(main_frame, text="提醒类型:").pack(anchor=tk.W, pady=(0, 5))
            type_frame = ttk.Frame(main_frame)
            type_frame.pack(fill=tk.X, pady=(0, 10))

            type_var = tk.StringVar(value="once")
            ttk.Radiobutton(type_frame, text="一次性", variable=type_var, value="once").pack(side=tk.LEFT, padx=(0, 10))
            ttk.Radiobutton(type_frame, text="每日", variable=type_var, value="daily").pack(side=tk.LEFT, padx=(0, 10))
            ttk.Radiobutton(type_frame, text="每周", variable=type_var, value="weekly").pack(side=tk.LEFT)

            # 时间输入 - 分成日期和时间两个输入框
            ttk.Label(main_frame, text="触发时间:").pack(anchor=tk.W, pady=(0, 5))
            time_frame = ttk.Frame(main_frame)
            time_frame.pack(fill=tk.X, pady=(0, 5))

            # 日期输入
            date_label = ttk.Label(time_frame, text="日期:")
            date_label.pack(side=tk.LEFT)
            date_entry = ttk.Entry(time_frame, width=12)
            date_entry.pack(side=tk.LEFT, padx=(5, 10))
            date_entry.insert(0, datetime.now().strftime("%Y-%m-%d"))

            # 时间输入
            ttk.Label(time_frame, text="时间:").pack(side=tk.LEFT)
            time_entry = ttk.Entry(time_frame, width=10)
            time_entry.pack(side=tk.LEFT, padx=(5, 0))
            # 使用当前时间作为默认值
            current_time = datetime.now().strftime("%H:%M:%S")
            time_entry.insert(0, current_time)

            # 添加格式提示
            tk.Label(main_frame, text="格式: 日期(YYYY-MM-DD) 时间(HH:MM:SS)",
                     foreground="gray").pack(anchor=tk.W, pady=(0, 10))

            # 星期选择（每周提醒用）
            week_frame = ttk.Frame(main_frame)
            week_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(week_frame, text="星期几 (1-7):").pack(side=tk.LEFT)
            week_entry = ttk.Entry(week_frame, width=5)
            week_entry.pack(side=tk.LEFT, padx=(5, 0))
            week_entry.insert(0, "1")

            # 初始隐藏星期选择
            week_frame.pack_forget()

            # 类型变化时的处理
            def on_type_change():
                if type_var.get() == "once":
                    date_label.pack(side=tk.LEFT)
                    date_entry.pack(side=tk.LEFT, padx=(5, 10))
                    week_frame.pack_forget()
                elif type_var.get() == "daily":
                    date_label.pack_forget()
                    date_entry.pack_forget()
                    week_frame.pack_forget()
                elif type_var.get() == "weekly":
                    date_label.pack_forget()
                    date_entry.pack_forget()
                    week_frame.pack(fill=tk.X, pady=(0, 10))

            # 绑定类型变化事件
            for widget in type_frame.winfo_children():
                if isinstance(widget, ttk.Radiobutton):
                    widget.configure(command=on_type_change)

            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(20, 0))

            def create_reminder():
                try:
                    title = title_entry.get().strip()
                    if not title:
                        messagebox.showerror("错误", "请输入提醒标题")
                        return

                    reminder_type = type_var.get()
                    date_str = date_entry.get().strip()
                    time_str = time_entry.get().strip()

                    # 验证输入
                    if not date_str:
                        messagebox.showerror("错误", "请输入日期")
                        return

                    if not time_str:
                        messagebox.showerror("错误", "请输入时间")
                        return

                    # 合并日期和时间
                    full_time = f"{date_str} {time_str}"

                    # 验证日期时间格式
                    try:
                        datetime.strptime(full_time, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        messagebox.showerror("错误", "日期时间格式不正确\n请使用格式: YYYY-MM-DD HH:MM:SS")
                        return

                    # 构建指令
                    command = f'REMINDER_ADD "{title}" "{full_time}" "{reminder_type}"'

                    # 执行指令
                    response = self.serial_comm.send_command(command)

                    if response and response.get("status") == "success":
                        self.logger.info(f"添加提醒成功: {title}")
                        dialog.destroy()
                        # 自动刷新提醒列表
                        self.root.after(100, self.refresh_reminder_list)
                        messagebox.showinfo("成功", "提醒添加成功")
                    else:
                        error_msg = response.get("error", "未知错误") if response else "无响应"
                        messagebox.showerror("错误", f"添加提醒失败: {error_msg}")

                except Exception as e:
                    messagebox.showerror("错误", f"添加提醒失败: {e}")

            def cancel():
                dialog.destroy()

            ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="添加", command=create_reminder).pack(side=tk.RIGHT)

            # 绑定回车键
            dialog.bind('<Return>', lambda e: create_reminder())
            dialog.bind('<Escape>', lambda e: cancel())

        except Exception as e:
            self.logger.error(f"显示添加提醒对话框失败: {e}")
            messagebox.showerror("错误", f"显示添加提醒对话框失败: {e}")

    def show_edit_reminder_dialog(self, reminder):
        """显示编辑提醒对话框"""
        try:
            # 创建对话框
            dialog = tk.Toplevel(self.root)
            dialog.title(f"编辑提醒 - {reminder['title']}")
            dialog.geometry("450x400")
            dialog.transient(self.root)
            dialog.grab_set()
            dialog.resizable(False, False)

            # 居中显示
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
            y = (dialog.winfo_screenheight() // 2) - (400 // 2)
            dialog.geometry(f"450x400+{x}+{y}")

            # 创建滚动框架
            canvas = tk.Canvas(dialog)
            scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 主内容区域
            main_frame = ttk.Frame(scrollable_frame)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 提醒ID显示
            ttk.Label(main_frame, text=f"提醒ID: {reminder['id']}").pack(anchor=tk.W, pady=(0, 10))

            # 标题编辑
            ttk.Label(main_frame, text="提醒标题:").pack(anchor=tk.W, pady=(0, 5))
            title_entry = ttk.Entry(main_frame, width=50)
            title_entry.pack(fill=tk.X, pady=(0, 10))
            title_entry.insert(0, reminder['title'])
            title_entry.focus_set()

            # 时间编辑 - 分成日期和时间两个输入框
            ttk.Label(main_frame, text="触发时间:").pack(anchor=tk.W, pady=(0, 5))

            time_frame = ttk.Frame(main_frame)
            time_frame.pack(fill=tk.X, pady=(0, 10))

            # 解析原始时间
            original_time = reminder['time']
            if ' ' in original_time:
                date_part, time_part = original_time.split(' ', 1)
            else:
                # 如果没有空格，假设是时间部分
                date_part = datetime.now().strftime("%Y-%m-%d")
                time_part = original_time

            # 日期输入
            ttk.Label(time_frame, text="日期:").pack(side=tk.LEFT)
            date_entry = ttk.Entry(time_frame, width=12)
            date_entry.pack(side=tk.LEFT, padx=(5, 10))
            date_entry.insert(0, date_part)

            # 时间输入
            ttk.Label(time_frame, text="时间:").pack(side=tk.LEFT)
            time_entry = ttk.Entry(time_frame, width=10)
            time_entry.pack(side=tk.LEFT, padx=(5, 0))
            time_entry.insert(0, time_part)

            # 添加格式提示
            tk.Label(main_frame, text="格式: 日期(YYYY-MM-DD) 时间(HH:MM:SS)",
                     foreground="gray").pack(anchor=tk.W, pady=(0, 10))

            # 类型编辑
            ttk.Label(main_frame, text="提醒类型:").pack(anchor=tk.W, pady=(0, 5))
            type_frame = ttk.Frame(main_frame)
            type_frame.pack(fill=tk.X, pady=(0, 10))

            type_var = tk.StringVar(value=reminder['type'])
            ttk.Radiobutton(type_frame, text="一次性", variable=type_var, value="once").pack(side=tk.LEFT, padx=(0, 10))
            ttk.Radiobutton(type_frame, text="每日", variable=type_var, value="daily").pack(side=tk.LEFT, padx=(0, 10))
            ttk.Radiobutton(type_frame, text="每周", variable=type_var, value="weekly").pack(side=tk.LEFT)

            # 状态显示
            ttk.Label(main_frame, text=f"当前状态: {reminder['status']}").pack(anchor=tk.W, pady=(10, 0))

            # 固定在底部的按钮区域
            button_frame = ttk.Frame(dialog)
            button_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)

            # 滚动区域
            canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=(10, 0))
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(10, 60))

            def save_changes():
                try:
                    new_title = title_entry.get().strip()
                    new_date = date_entry.get().strip()
                    new_time_part = time_entry.get().strip()
                    new_type = type_var.get()

                    # 验证输入格式
                    if not new_title:
                        messagebox.showerror("错误", "请输入提醒标题")
                        return

                    if not new_date:
                        messagebox.showerror("错误", "请输入日期")
                        return

                    if not new_time_part:
                        messagebox.showerror("错误", "请输入时间")
                        return

                    # 合并日期和时间
                    new_time = f"{new_date} {new_time_part}"

                    # 验证日期时间格式
                    try:
                        datetime.strptime(new_time, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        messagebox.showerror("错误", "日期时间格式不正确\n请使用格式: YYYY-MM-DD HH:MM:SS")
                        return

                    # 检查是否有变化
                    changes = []
                    if new_title != reminder['title']:
                        changes.append(('title', new_title))
                    if new_time != reminder['time']:
                        changes.append(('time', new_time))
                    if new_type != reminder['type']:
                        changes.append(('type', new_type))

                    if not changes:
                        messagebox.showinfo("提示", "没有检测到任何更改")
                        return

                    # 使用REMINDER_MOD指令一次性修改所有字段
                    command = f'REMINDER_MOD {reminder["id"]} "{new_title}" "{new_time}" "{new_type}"'
                    response = self.serial_comm.send_command(command)

                    if response and response.get("status") == "success":
                        success_count = 1
                    else:
                        error_msg = response.get("error", "未知错误") if response else "无响应"
                        messagebox.showerror("错误", f"修改提醒失败: {error_msg}")
                        success_count = 0

                    if success_count > 0:
                        self.logger.info(f"编辑提醒成功: {reminder['title']}")
                        dialog.destroy()
                        # 自动刷新提醒列表
                        self.root.after(100, self.refresh_reminder_list)
                        messagebox.showinfo("成功", "提醒修改成功")

                except Exception as e:
                    messagebox.showerror("错误", f"保存更改失败: {e}")

            def cancel():
                dialog.destroy()

            ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.RIGHT, padx=(5, 0))
            ttk.Button(button_frame, text="保存", command=save_changes).pack(side=tk.RIGHT)

            # 绑定回车键
            dialog.bind('<Return>', lambda e: save_changes())
            dialog.bind('<Escape>', lambda e: cancel())

        except Exception as e:
            self.logger.error(f"显示编辑提醒对话框失败: {e}")
            messagebox.showerror("错误", f"显示编辑提醒对话框失败: {e}")

